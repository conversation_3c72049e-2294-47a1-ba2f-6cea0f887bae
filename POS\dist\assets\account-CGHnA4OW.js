import{j as t,B as m,r as c,L as p}from"./index-DLSysrh4.js";import{H as l}from"./header-BO8CjX_w.js";import{P as d}from"./profile-dropdown-D5DnkWo7.js";import{S as h,T as x}from"./search-CCOrl5KY.js";import"./date-range-picker-CoReVTN3.js";import"./form-DCttU9FS.js";import{D as g}from"./data-table-DIgSxz1h.js";import{B as u}from"./badge-CSxw3ZSI.js";import{S as j}from"./settings-DeXBUVW7.js";import{u as f,n as v,A as y}from"./navigation-BG6Fwlig.js";import"./separator-DsrkJotP.js";import"./avatar-C2xen_AP.js";import"./dropdown-menu-DxrvXCFj.js";import"./index-BvxNbdt9.js";import"./index-BmCCDDB3.js";import"./index-QWmA5vou.js";import"./check-B956xcJg.js";import"./createLucideIcon-BCCeA4NW.js";import"./search-context-GGIHanUV.js";import"./command-WbkkLvMj.js";import"./calendar-DaAHj6Vo.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DGVckEWq.js";import"./search-CX_1dbj5.js";import"./createReactComponent-CV7I4pf9.js";import"./pos-api-CYJtTNYA.js";import"./scroll-area-DCgoUvjl.js";import"./select-LfFpGbbb.js";import"./index-D_lRkZQY.js";import"./IconChevronRight-BwCnOkVS.js";import"./IconSearch-K-wA4kRr.js";import"./chevron-right-CzKIkqDA.js";import"./react-icons.esm-tBZuCPpJ.js";import"./popover-BVjXu8gX.js";import"./table-pagination-B3mS26G7.js";import"./pagination-BxHpfqqN.js";import"./table-yR0osUgs.js";function T({users:i,isLoading:s,onEditUser:o,onToggleStatus:a}){const n=[{key:"username",header:"Tên người dùng",width:"200px"},{key:"email",header:"Email",width:"250px"},{key:"status",header:"Trạng thái",width:"120px",render:r=>t.jsx(u,{variant:r==="active"?"default":"secondary",children:r==="active"?"Hoạt động":"Không hoạt động"})},{key:"actions",header:"",width:"100px",render:(r,e)=>t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>o(e),className:"h-8 w-8",children:t.jsx(j,{className:"h-4 w-4"})}),t.jsx(m,{variant:"ghost",size:"icon",onClick:()=>a(e.id),className:"h-8 w-8",children:e.status==="active"?"Hủy":"Kích hoạt"})]})}];return s?t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{children:"Đang tải dữ liệu..."})}):t.jsx(g,{data:i,columns:n,isLoading:s,pageSize:20,emptyMessage:"Không có tài khoản nào",loadingMessage:"Đang tải..."})}const N=({error:i})=>t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"py-8 text-center",children:t.jsx("p",{className:"text-red-600",children:i})})}),k=()=>t.jsx("div",{className:"mb-6",children:t.jsx(p,{to:y.CREATE,children:t.jsx(m,{children:"Tạo tài khoản"})})});function S(){const{users:i,isLoading:s,error:o,toggleUserStatus:a}=f(),n=c.useCallback(e=>{v(e.id)},[]),r=c.useCallback(async e=>{await a(e)},[a]);return o?t.jsx(N,{error:o}):t.jsxs(t.Fragment,{children:[t.jsxs(l,{children:[t.jsx(h,{}),t.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[t.jsx(x,{}),t.jsx(d,{})]})]}),t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(k,{}),t.jsx(T,{users:i,isLoading:s,onEditUser:n,onToggleStatus:r})]})]})}const mt=S;export{mt as component};
