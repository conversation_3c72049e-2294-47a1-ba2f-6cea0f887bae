import{u as Ie,l as Pe,a as ze,r as z,h as re,j as e,B as P,T as Te,o as Ve,p as Fe,q as Oe}from"./index-DLSysrh4.js";import{a as h,b as g,c as d,d as f,e as C,k as Be,u as qe,F as Me}from"./form-DCttU9FS.js";import{s as Ue}from"./zod-j2m7p1zB.js";import{u as Ae}from"./use-upload-image-DTozGwxx.js";import{k as Ee,P as Ce,B as Ke,l as Le,j as Qe,i as $e,m as He,n as ce}from"./price-source-dialog-DBllyoO8.js";import"./pos-api-CYJtTNYA.js";import"./user-QGmeemH2.js";import"./vietqr-api-CdtuA3dS.js";import"./crm-api-B_UJzTcg.js";import{u as Xe}from"./use-item-types-CSsfT6lm.js";import{u as We}from"./use-item-classes-DGMAg27S.js";import{u as Ge}from"./use-units-gLxkZ0xs.js";import{u as Ye}from"./use-items-BP05OniH.js";import{u as Je}from"./use-removed-items-Do_IGH04.js";import{u as Ze}from"./use-customizations-CPiPqWYz.js";import{u as De}from"./use-customization-by-id-BZnh-lz-.js";import{u as Re}from"./use-sources-CYxjcLqt.js";import{X as oe}from"./calendar-DaAHj6Vo.js";import{C as E}from"./checkbox-C0erTYQj.js";import{I as O}from"./input-42MGwRgA.js";import{T as es}from"./textarea-DA1C5L_X.js";import{C as H}from"./combobox-Cw6fZwGg.js";import{D as ss,a as ts,b as is,c as ns}from"./dialog-DGVckEWq.js";import{U as ke}from"./upload-Coojo4x4.js";import{C as ls,a as as,b as cs}from"./collapsible-u38SogMV.js";import{C as rs}from"./confirm-dialog-DflbPhcO.js";import"./header-BO8CjX_w.js";import"./main-W6TPZi-0.js";import"./search-context-GGIHanUV.js";import"./date-range-picker-CoReVTN3.js";import"./multi-select-DB8GW7bS.js";import"./exceljs.min-Bt6AOS27.js";import"./core.esm-7ZEtfXI_.js";import{C as os}from"./circle-help-Bn10grtQ.js";import{C as ds}from"./select-LfFpGbbb.js";import{C as ms}from"./chevron-right-CzKIkqDA.js";function Se({form:t}={}){const m=t==null?void 0:t.watch("city_uid"),p=t==null?void 0:t.watch("customization_uid"),{company:j}=Ie(M=>M.auth),{selectedBrand:x}=Pe(),{data:v=[]}=Re({company_uid:j==null?void 0:j.id,brand_uid:x==null?void 0:x.id,skip_limit:!0,enabled:!!(j!=null&&j.id)&&!!(x!=null&&x.id)}),{data:k=[]}=Ze({skip_limit:!0,list_city_uid:m!=="all"?[m||""]:void 0,enabled:!!m}),{data:b}=De(p||"",!!p&&p!=="none"),{data:y=[]}=Ye({params:{city_uid:m,skip_limit:!0},enabled:!!m}),{data:S=[]}=Je(),{data:I=[]}=Xe(),{data:_=[]}=Ge(),{data:N=[]}=We({skip_limit:!0}),{stores:V}=ze();return{selectedCityUid:m,selectedCustomizationUid:p,company:j,selectedBrand:x,customizations:k,customizationDetails:b,items:y,sourcesData:v,cities:S,stores:V,itemTypes:I,units:_,itemClasses:N}}function us(){const[t,m]=z.useState(!1),[p,j]=z.useState(!1),[x,v]=z.useState(!1),[k,b]=z.useState(!1),[y,S]=z.useState(null);return{showQuantityInputs:t,isCustomizationDetailsOpen:p,isPriceSourceDialogOpen:x,isBuffetConfigModalOpen:k,confirmDeleteIndex:y,setShowQuantityInputs:m,setIsCustomizationDetailsOpen:j,setIsPriceSourceDialogOpen:v,setIsBuffetConfigModalOpen:b,setConfirmDeleteIndex:S,toggleQuantityInputs:()=>{m(!t)},openPriceSourceDialog:()=>{v(!0)},closePriceSourceDialog:()=>{v(!1)},openBuffetConfigModal:()=>{b(!0)},closeBuffetConfigModal:()=>{b(!1)},handleRemovePriceSource:u=>{S(u)},clearConfirmDelete:()=>{S(null)}}}function hs({isUpdate:t,currentRow:m,isLoading:p,onSave:j,onDeactive:x,onActive:v,isDeactivating:k=!1,isActivating:b=!1}){const y=re(),S=()=>{y({to:"/menu/items/items-in-store"})},I=()=>t?"Chi tiết món":"Tạo món";return e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{children:e.jsx(P,{variant:"ghost",size:"sm",onClick:S,className:"flex items-center",children:e.jsx(oe,{className:"h-4 w-4"})})}),e.jsx("h2",{className:"mb-2 text-3xl font-medium",children:I()}),e.jsxs("div",{className:"flex gap-2",children:[t&&(m==null?void 0:m.active)&&x&&e.jsx(P,{type:"button",variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50",disabled:k,onClick:x,children:k?"Đang deactive...":"Deactive"}),t&&!(m!=null&&m.active)&&v&&e.jsx(P,{type:"button",variant:"outline",className:"border-green-500 text-green-500 hover:bg-green-50",disabled:b,onClick:v,children:b?"Đang active...":"Active"}),e.jsx(P,{type:"button",disabled:p,onClick:j,children:p?"Đang lưu...":"Lưu"})]})]})})}const xs=["#3B82F6","#EF4444","#10B981","#F59E0B","#8B5CF6","#EC4899","#F97316","#84CC16","#06B6D4","#6366F1"],_s=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9"];function gs({open:t,onOpenChange:m,onImageSelect:p,onColorSelect:j,onSelfOrderToggle:x,selectedColor:v="#000000",useSelfOrderImage:k=!1,currentImageUrl:b}){const[y,S]=z.useState(null),[I,_]=z.useState(null),N=b||y,V=u=>{u||(_(null),S(null)),m(u)},M=u=>{var a;const l=(a=u.target.files)==null?void 0:a[0];if(l){_(l);const w=new FileReader;w.onload=X=>{var Q;S((Q=X.target)==null?void 0:Q.result)},w.readAsDataURL(l)}},K=()=>{I?p(I):v&&v!=="#000000"&&j(v),V(!1)},A=u=>{j(u)};return e.jsx(ss,{open:t,onOpenChange:V,children:e.jsxs(ts,{className:"max-h-[90vh] w-[95vw] max-w-3xl overflow-y-auto lg:max-w-2xl",children:[e.jsx(is,{children:e.jsx(ns,{className:"text-lg font-semibold",children:"Chọn ảnh hoặc màu"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Chọn ảnh"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Bạn có thể chọn ảnh có kích thước 540x785px để sử dụng trên thiết bị Self Order"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(E,{checked:k,onCheckedChange:x}),e.jsx("label",{className:"text-sm font-medium",children:"Sử dụng ảnh cho thiết bị Self Order"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-4 transition-colors hover:bg-gray-100",style:{width:"467px",height:"420px",maxWidth:"100%",maxHeight:"380px"},onClick:()=>{var u;return(u=document.getElementById("dialog-image-upload"))==null?void 0:u.click()},children:N?e.jsx("img",{src:N,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"text-center",children:[e.jsx(ke,{className:"mx-auto mb-2 h-8 w-8 text-gray-400"}),e.jsx("span",{className:"text-sm text-gray-500",children:"Click để chọn ảnh"})]})}),e.jsx("input",{type:"file",accept:"image/*",onChange:M,className:"hidden",id:"dialog-image-upload"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Hoặc chọn màu dưới đây"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"flex space-x-2",children:xs.map(u=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${v===u?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:u},onClick:()=>A(u)},u))}),e.jsx(O,{type:"text",value:v,onChange:u=>A(u.target.value),className:"w-24 text-center",placeholder:"#000000"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Khác"}),e.jsx("div",{className:"flex space-x-2",children:_s.map(u=>e.jsx("button",{className:`h-8 w-8 rounded-full border-2 transition-all ${v===u?"scale-110 border-gray-900":"border-gray-300 hover:border-gray-400"}`,style:{backgroundColor:u},onClick:()=>A(u)},u))})]})]})]}),e.jsxs("div",{className:"flex justify-end space-x-2 pt-4",children:[e.jsx(P,{variant:"outline",onClick:()=>m(!1),children:"Huỷ"}),e.jsx(P,{onClick:K,children:"Xong"})]})]})})}function ps({form:t,mode:m,itemTypes:p,itemClasses:j,units:x,stores:v,onImageChange:k,imagePreview:b,onImageRemove:y}){const[S,I]=z.useState(!1),[_,N]=z.useState(()=>{const l=t.getValues("item_color");return l&&l.trim()!==""?l:"#000000"}),[V,M]=z.useState(!1),K=l=>{k({target:{files:[l]}}),N("#000000"),t.setValue("item_color","")},A=l=>{N(l),t.setValue("item_color",l),y&&y()},u=l=>{M(l)};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Chi tiết"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-8 lg:grid-cols-12",children:[e.jsxs("div",{className:"space-y-6 lg:col-span-9",children:[e.jsx(h,{control:t.control,name:"item_name",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Tên ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{placeholder:"Nhập tên món",className:"w-full",...l})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"ots_price",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Giá ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{placeholder:"0",className:"w-full",value:l.value?new Intl.NumberFormat("vi-VN").format(l.value):"",onChange:a=>{const w=a.target.value.replace(/[^\d]/g,"");l.onChange(w?Number(w):0)}})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"item_id",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã món"}),e.jsx(f,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(O,{placeholder:"Nếu để trống, hệ thống sẽ tự động tạo một mã món",className:"w-full",...l,readOnly:m==="update"||!t.watch("enable_custom_item_id")}),m==="create"&&e.jsx(h,{control:t.control,name:"enable_custom_item_id",render:({field:a})=>e.jsx(f,{children:e.jsx(E,{checked:a.value===1,onCheckedChange:w=>{a.onChange(w)},className:"border-2 border-blue-500 data-[state=checked]:border-blue-500 data-[state=checked]:bg-blue-500"})})})]})})]}),e.jsx(C,{})]})})]}),e.jsx("div",{className:"lg:col-span-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-2 transition-colors hover:bg-gray-100",style:{height:"156px",backgroundColor:_&&_!=="#000000"?_:void 0},onClick:()=>I(!0),children:b?e.jsx("img",{src:b,alt:"Preview",className:"h-full w-full rounded-lg object-cover"}):_&&_!=="#000000"?e.jsx("div",{className:"h-full w-full rounded-lg",style:{backgroundColor:_}}):e.jsxs(e.Fragment,{children:[e.jsx(ke,{className:"mb-1 h-6 w-6 text-gray-400"}),e.jsx("span",{className:"text-center text-xs text-gray-500",children:"Chọn ảnh"})]})}),(b||_&&_!=="#000000")&&e.jsx("button",{type:"button",onClick:()=>{b&&y&&y(),N("#000000"),t.setValue("item_color","")},className:"absolute -top-2 -right-2 rounded-full bg-gray-600 p-1 text-white transition-colors hover:bg-gray-700",children:e.jsx(oe,{className:"h-3 w-3"})}),e.jsx("input",{type:"file",accept:"image/*",onChange:k,className:"hidden",id:"image-upload"})]})})]}),e.jsx(h,{control:t.control,name:"item_id_barcode",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mã barcode"}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{placeholder:"Nếu bạn sử dụng tính năng scan QR thì POS hay tạo mã barcode",className:"w-full",maxLength:15,...l})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"is_eat_with",render:({field:l})=>e.jsx(g,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Món ăn kèm"}),e.jsx(f,{children:e.jsx(E,{checked:l.value===1,onCheckedChange:a=>l.onChange(a?1:0)})})]})})}),e.jsx(h,{control:t.control,name:"no_update_quantity_toping",render:({field:l})=>e.jsx(g,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Không cập nhật số lượng món ăn kèm"}),e.jsx(f,{children:e.jsx(E,{checked:l.value===1,onCheckedChange:a=>l.onChange(a?1:0)})})]})})}),e.jsx(h,{control:t.control,name:"item_type_uid",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Nhóm"}),e.jsx(f,{className:"flex-1",children:e.jsx(H,{options:p.map(a=>({value:a.id,label:a.item_type_name,disabled:a.active===0,status:a.active===0?"Deactive":void 0,statusClassName:a.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:a=>l.onChange(a||void 0),placeholder:"Uncategory",searchPlaceholder:"Tìm kiếm...",emptyText:"Không tìm thấy nhóm.",className:"flex-1 text-blue-500"})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"item_class_uid",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Loại món"}),e.jsx(f,{className:"flex-1",children:e.jsx(H,{options:j.map(a=>({value:a.id,label:a.item_class_name,disabled:a.active===0,status:a.active===0?"Deactive":void 0,statusClassName:a.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:a=>l.onChange(a||void 0),placeholder:"None",searchPlaceholder:"Tìm kiếm loại món...",emptyText:"Không tìm thấy loại món.",className:"flex-1 text-blue-500"})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"description",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Mô tả"}),e.jsx(f,{className:"flex-1",children:e.jsx(es,{placeholder:"Nếu để trống thì tên món sẽ tự động làm mô tả món",className:"min-h-[80px] w-full",...l})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"apply_with_store",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Trạng thái"}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{className:"w-full",value:Number(l.value)===1?"Sửa từ món gốc":"Món mới",readOnly:!0})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"store_uid",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Cửa hàng áp dụng ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{className:"flex-1",children:e.jsx(H,{options:v.map(a=>({value:a.id,label:a.store_name,disabled:a.active===0,status:a.active===0?"Deactive":void 0,statusClassName:a.active===0?"bg-red-100 text-red-700":void 0})),value:l.value,onValueChange:a=>l.onChange(a||void 0),placeholder:"Chọn cửa hàng",searchPlaceholder:"Tìm cửa hàng...",emptyText:"Không tìm thấy cửa hàng.",className:"flex-1 text-blue-500",disabled:m==="update"})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"item_id_mapping",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"SKU"}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{placeholder:"Nhập mã SKU",className:"w-full",maxLength:50,...l})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"unit_uid",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:["Đơn vị tính ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(f,{className:"flex-1",children:e.jsx(H,{options:x.map(a=>({value:a.id,label:a.unit_name||a.id})),value:l.value,onValueChange:a=>l.onChange(a||void 0),placeholder:"Món",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"unit_secondary_uid",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Đơn vị tính thứ 2"}),e.jsx(f,{className:"flex-1",children:e.jsx(H,{options:x.map(a=>({value:a.id,label:a.unit_name||a.id})),value:l.value,onValueChange:a=>l.onChange(a||void 0),placeholder:"Chọn đơn vị tính",searchPlaceholder:"Tìm kiếm đơn vị tính...",emptyText:"Không tìm thấy đơn vị tính.",className:"flex-1 text-blue-500"})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"ots_tax",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"VAT món ăn"}),e.jsx(f,{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{type:"number",placeholder:"0",className:"w-full",value:l.value?Math.round(l.value*100):"",onChange:a=>{const w=a.target.value;l.onChange(w?Number(w)/100:0)}}),e.jsx("span",{children:"%"})]})})]}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"time_cooking",render:({field:l})=>e.jsxs(g,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(d,{className:"w-72 flex-shrink-0 text-left text-sm font-medium",children:"Thời gian chế biến (phút)"}),e.jsx(f,{className:"flex-1",children:e.jsx(O,{type:"number",placeholder:"0",className:"w-full",value:l.value?Math.round(l.value/6e4):"",onChange:a=>{const w=a.target.value;l.onChange(w?Number(w)*6e4:0)}})})]}),e.jsx(C,{})]})})]})]}),e.jsx(gs,{open:S,onOpenChange:I,onImageSelect:K,onColorSelect:A,onSelfOrderToggle:u,selectedColor:_,useSelfOrderImage:V,currentImageUrl:b||void 0})]})}function js({form:t}){var R,ee;const m=re(),[p,j,x,v,k,b]=Be({control:t.control,name:["is_virtual_item","is_buffet_item","cross_price","price_by_source","exclude_items_buffet","up_size_buffet"]}),{selectedCustomizationUid:y,customizations:S,customizationDetails:I,items:_,sourcesData:N}=Se({form:t}),{showQuantityInputs:V,isCustomizationDetailsOpen:M,isPriceSourceDialogOpen:K,isBuffetConfigModalOpen:A,setIsCustomizationDetailsOpen:u,setIsPriceSourceDialogOpen:l,setIsBuffetConfigModalOpen:a,openPriceSourceDialog:w,openBuffetConfigModal:X,toggleQuantityInputs:Q,handleRemovePriceSource:J,clearConfirmDelete:Z,confirmDeleteIndex:W}=us(),[s,F]=z.useState(null),[te,G]=z.useState(null),ie=i=>{const c=t.getValues("price_by_source")||[];if(s!==null){const r=[...c];r[s]={source_id:i.source_id,price:i.price,source_name:i.source_name,price_times:i.price_times,is_source_exist_in_city:i.is_source_exist_in_city},t.setValue("price_by_source",r),F(null),G(null)}else{const r=[...c,{source_id:i.source_id,price:i.price,source_name:i.source_name,price_times:i.price_times,is_source_exist_in_city:i.is_source_exist_in_city}];t.setValue("price_by_source",r)}},D=(i,c)=>{F(c),G(i),l(!0)},ne=i=>{t.setValue("exclude_items_buffet",i)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình sửa giá, nhập số lượng, bỏ món"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Cho phép sửa giá khi bán"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"enable_edit_price",render:({field:i})=>e.jsx(g,{children:e.jsx(f,{children:e.jsx(E,{checked:(i.value&2)===2,onCheckedChange:c=>{const r=c?i.value|2:i.value&-3;i.onChange(r)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Yêu cầu nhập số lượng khi gọi món"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"enable_edit_price",render:({field:i})=>e.jsx(g,{children:e.jsx(f,{children:e.jsx(E,{checked:(i.value&4)===4,onCheckedChange:c=>{const r=c?i.value|4:i.value&-5;i.onChange(r)}})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-left font-medium text-gray-700",children:"Cho phép bỏ món mà không cần quyền áp dụng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"enable_edit_price",render:({field:i})=>e.jsx(g,{children:e.jsx(f,{children:e.jsx(E,{checked:(i.value&8)===8,onCheckedChange:c=>{const r=c?i.value|8:i.value&-9;i.onChange(r)}})})})})})]})]}),e.jsxs("div",{className:"space-y-4 border-t pt-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ảo"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"is_virtual_item",render:({field:i})=>e.jsx(g,{children:e.jsx(f,{children:e.jsx(E,{checked:!!i.value,onCheckedChange:c=>i.onChange(c?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Cấu hình món ăn là vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"is_buffet_item",render:({field:i})=>e.jsx(g,{children:e.jsx(f,{children:e.jsx(E,{checked:!!i.value,onCheckedChange:c=>i.onChange(c?1:0)})})})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Công thức inQr cho máy pha trà"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"formula_qrcode",render:({field:i})=>e.jsxs(g,{children:[e.jsx(f,{children:e.jsx(O,{placeholder:"Nhập công thức InQR",...i})}),e.jsx(C,{})]})})})]}),j===1&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Danh sách món không đi kèm vé buffet"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(P,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:X,children:[k.length," món"]})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Danh sách vé buffet được upsize"}),e.jsx("div",{className:"col-span-2",children:e.jsxs(P,{type:"button",variant:"outline",className:"w-full justify-start text-blue-600",onClick:()=>{},children:[b.length," món"]})})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình món dịch vụ"}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Cấu hình món dịch vụ"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"is_service",render:({field:i})=>e.jsx(g,{children:e.jsx("div",{className:"flex items-center gap-4",children:e.jsx(f,{children:e.jsx(E,{checked:!!i.value,onCheckedChange:c=>i.onChange(!!c)})})})})})})]}),e.jsxs("div",{className:"space-y-4",children:[t.watch("is_service")?e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Cấu hình giá thay đổi theo số lượng"}),e.jsx(Te,{children:e.jsxs(Ve,{children:[e.jsx(Fe,{asChild:!0,children:e.jsx(os,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(Oe,{children:e.jsx("p",{children:"Nếu khai báo cấu hình đổi giá theo số lượng thì món sẽ ko tự động áp dụng giá và giảm giá theo thời gian khi chạy của món dịch vụ nữa, chỉ được chọn 1 trong 2"})})]})})]}),e.jsx(P,{type:"button",size:"sm",variant:V||x.length>0?"outline":"default",className:V||x.length>0?"":"bg-blue-500 hover:bg-blue-600",onClick:Q,children:V||x.length>0?"Xoá":"Thêm"})]}),(V||x.length>0)&&e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Khai báo số lượng"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"quantity",render:({field:i})=>e.jsxs(g,{children:[e.jsx(f,{children:e.jsx(O,{type:"number",placeholder:"1",className:"bg-white",...i,value:i.value||"",onChange:c=>{const r=Number(c.target.value)||0;i.onChange(r);const o=t.getValues("price")||0;t.setValue("cross_price",[{quantity:r,price:o}])}})}),e.jsx(C,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Khai báo giá khi vượt qua số lượng trên"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"price",render:({field:i})=>e.jsxs(g,{children:[e.jsx(f,{children:e.jsx(O,{placeholder:"0",className:"bg-white",value:i.value||"",onChange:c=>{const r=Number(c.target.value)||0;i.onChange(r);const o=t.getValues("quantity")||0;t.setValue("cross_price",[{quantity:o,price:r}])}})}),e.jsx(C,{})]})})})]})]})})]}):null,!p&&e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Cấu hình giá theo nguồn"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(d,{className:"font-medium text-gray-700",children:"Cấu hình giá theo nguồn"}),e.jsx(P,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:w,children:"Thêm nguồn"})]}),v.length>0&&e.jsx("div",{className:"space-y-2",children:v.map((i,c)=>e.jsxs("div",{className:"flex cursor-pointer items-center justify-between rounded-md border p-3 hover:bg-gray-50",onClick:()=>D(i,c),children:[e.jsx("div",{children:e.jsxs("span",{className:"font-medium",children:[Ee(i.source_id,N)||i.sourceName," - Số tiền:"," ",i.price.toLocaleString()," ₫"]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{type:"button",variant:"ghost",size:"sm",onClick:r=>{r.stopPropagation(),D(i,c)},className:"text-blue-600 hover:text-blue-700",children:"Sửa"}),e.jsx(P,{type:"button",variant:"ghost",size:"sm",onClick:r=>{r.stopPropagation(),J(c)},className:"text-gray-400 hover:text-gray-600",children:e.jsx(oe,{className:"h-4 w-4"})})]})]},c))})]})]}),t.watch("city_uid")&&e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Customization"}),e.jsx(P,{type:"button",size:"sm",className:"bg-blue-500 hover:bg-blue-600",onClick:()=>m({to:"/menu/customization/customization-in-city/detail"}),children:"Tạo customization"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Customization"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"customization_uid",render:({field:i})=>e.jsxs(g,{children:[e.jsx(H,{options:[{value:"none",label:"Không có"},...S.map(c=>({value:c.id,label:c.name}))],value:i.value??"none",onValueChange:c=>i.onChange(c==="none"?null:c),placeholder:"Chọn customization",searchPlaceholder:"Tìm kiếm customization...",emptyText:"Không tìm thấy customization.",className:"w-full"}),e.jsx(C,{})]})})})]}),y&&y!=="none"&&I&&e.jsx("div",{className:"space-y-4",children:e.jsxs(ls,{open:M,onOpenChange:u,children:[e.jsx(as,{asChild:!0,children:e.jsxs(P,{type:"button",variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsx("span",{className:"text-blue-600",children:"Chi tiết Customize"}),M?e.jsx(ds,{className:"h-4 w-4 text-blue-600"}):e.jsx(ms,{className:"h-4 w-4 text-blue-600"})]})}),e.jsx(cs,{className:"space-y-4",children:(ee=(R=I.data)==null?void 0:R.LstItem_Options)==null?void 0:ee.map(i=>{const c=i.LstItem_Id.map(r=>{const o=_.find(B=>B.item_id===r);return{id:(o==null?void 0:o.id)||r,name:(o==null?void 0:o.item_name)||r,price:(o==null?void 0:o.ots_price)||0,code:r,active:(o==null?void 0:o.active)??1}});return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:i.Name}),e.jsxs("span",{className:"ml-2 text-sm text-gray-500",children:["(Chọn từ ",i.Min_Permitted," đến ",i.Max_Permitted," món)"]})]}),e.jsx("div",{className:"grid grid-cols-2 gap-3 md:grid-cols-3 lg:grid-cols-4",children:c.map(r=>{const o=r.active===1;return e.jsxs("div",{className:`rounded-md border p-3 text-center ${o?"bg-gray-50":"cursor-not-allowed bg-gray-100 opacity-50"}`,children:[e.jsx("p",{className:`text-sm font-medium ${o?"":"text-gray-400"}`,children:r.name}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["(",r.code,")"]}),e.jsxs("p",{className:`mt-1 text-sm font-medium ${o?"text-green-600":"text-gray-400"}`,children:[r.price.toLocaleString("vi-VN",{minimumFractionDigits:0,maximumFractionDigits:0})," ","₫"]})]},r.id)})})]},i.id)})})]})})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(d,{className:"text-sm font-medium text-gray-700",children:"Khung thời gian bán"}),e.jsx(h,{control:t.control,name:"time_sale_date_week",render:({field:i})=>e.jsxs(g,{children:[e.jsx(d,{className:"text-sm text-gray-600",children:"Chọn ngày"}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:[{name:"Thứ 2",bit:4},{name:"Thứ 3",bit:8},{name:"Thứ 4",bit:16},{name:"Thứ 5",bit:32},{name:"Thứ 6",bit:64},{name:"Thứ 7",bit:128},{name:"Chủ nhật",bit:2}].map(({name:c,bit:r})=>{const o=r,B=(i.value&o)!==0;return e.jsx(P,{type:"button",variant:B?"default":"outline",size:"sm",className:`text-xs ${B?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const L=i.value||0,$=B?L&~o:L|o;i.onChange($)},children:c},c)})}),e.jsx(C,{})]})}),e.jsx(h,{control:t.control,name:"time_sale_hour_day",render:({field:i})=>e.jsxs(g,{children:[e.jsx(d,{className:"text-sm text-gray-600",children:"Chọn giờ"}),e.jsx("div",{className:"grid grid-cols-10 gap-2",children:Array.from({length:24},(c,r)=>({hour:r,label:`${r}h`})).map(({hour:c,label:r})=>{const o=1<<c,B=(i.value&o)!==0;return e.jsx(P,{type:"button",variant:B?"default":"outline",size:"sm",className:`text-xs ${B?"bg-blue-600 text-white":"border-blue-600 text-blue-600"}`,onClick:()=>{const L=i.value||0,$=B?L&~o:L|o;i.onChange($)},children:r},c)})}),e.jsx(C,{})]})})]}),e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"grid grid-cols-3 items-center gap-4",children:[e.jsx(d,{className:"text-right font-medium text-gray-700",children:"Thứ tự hiển thị"}),e.jsx("div",{className:"col-span-2",children:e.jsx(h,{control:t.control,name:"sort",render:({field:i})=>e.jsxs(g,{children:[e.jsx(f,{children:e.jsx(O,{type:"number",placeholder:"Nhập số thứ tự hiển thị",...i,onChange:c=>i.onChange(Number(c.target.value))})}),e.jsx(C,{})]})})})]})})]})]}),e.jsx(Ce,{open:K,onOpenChange:l,onConfirm:ie,sources:N,data:te}),e.jsx(Ke,{itemsBuffet:k||[],open:A,onOpenChange:a,onItemsChange:ne,items:_}),e.jsx(rs,{open:W!==null,onOpenChange:i=>!i&&Z(),title:"Bạn có muốn bỏ cấu hình ?",desc:"",confirmText:"Xóa",cancelBtnText:"Hủy",handleConfirm:()=>{if(W===null)return;const c=(t.getValues("price_by_source")||[]).filter((r,o)=>o!==W);t.setValue("price_by_source",c),Z()}})]})}function fs({form:t,mode:m,itemTypes:p,itemClasses:j,units:x,stores:v,onImageChange:k,imagePreview:b,onImageRemove:y}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx(ps,{form:t,mode:m,itemTypes:p,itemClasses:j,units:x,stores:v,onImageChange:k,imagePreview:b,onImageRemove:y}),e.jsx(js,{form:t})]})}const vs=He;function it({currentRow:t,isCopyMode:m=!1}){var i,c,r,o,B,L,$,de,me,ue,he,xe,_e,ge,pe,je,fe,ve,be,Ne;const p=!!t&&!m,j=!!t&&m,x=re(),[v,k]=z.useState(!1),[b,y]=z.useState(null),[S,I]=z.useState(null),{company:_,selectedBrand:N,sourcesData:V,itemTypes:M,units:K,itemClasses:A,stores:u}=Se(),{createItemAsync:l,isPending:a}=Le(),{updateItemAsync:w,isPending:X}=Qe(),{updateStatusAsync:Q,isPending:J}=$e(),{uploadImage:Z,isUploading:W}=Ae(),s=t;z.useEffect(()=>{s!=null&&s.image_path&&y(s.image_path)},[s==null?void 0:s.image_path,j]);const F=qe({resolver:Ue(vs),defaultValues:{item_name:(s==null?void 0:s.item_name)||"",ots_price:Number(s==null?void 0:s.ots_price)||0,description:(s==null?void 0:s.description)||"",item_id_barcode:j?"":(s==null?void 0:s.item_id_barcode)||"",is_eat_with:Number(s==null?void 0:s.is_eat_with)||0,item_class_uid:(s==null?void 0:s.item_class_uid)||"",item_type_uid:(s==null?void 0:s.item_type_uid)||"",store_uid:((c=(i=s==null?void 0:s.Stores)==null?void 0:i[0])==null?void 0:c.id)||"",city_uid:((o=(r=s==null?void 0:s.Stores)==null?void 0:r[0])==null?void 0:o.city_uid)||"",item_id:j?"":(s==null?void 0:s.item_id)||"",enable_custom_item_id:p&&s!=null&&s.item_id&&(s==null?void 0:s.item_id).trim()!==""?1:0,unit_uid:(s==null?void 0:s.unit_uid)||"",unit_secondary_uid:(s==null?void 0:s.unit_secondary_uid)||"",ots_tax:Number(s==null?void 0:s.ots_tax)||0,time_cooking:Number(s==null?void 0:s.time_cooking)||0,ta_price:Number(s==null?void 0:s.ta_price)||0,ta_tax:Number(s==null?void 0:s.ta_tax)||0,item_id_mapping:(s==null?void 0:s.item_id_mapping)||"",enable_edit_price:Number((B=s==null?void 0:s.extra_data)==null?void 0:B.enable_edit_price)||0,is_print_label:Number(s==null?void 0:s.is_print_label)||0,is_allow_discount:Number(s==null?void 0:s.is_allow_discount)||0,is_virtual_item:Number((L=s==null?void 0:s.extra_data)==null?void 0:L.is_virtual_item)||0,is_item_service:Number(($=s==null?void 0:s.extra_data)==null?void 0:$.is_item_service)||0,is_buffet_item:Number((de=s==null?void 0:s.extra_data)==null?void 0:de.is_buffet_item)||0,no_update_quantity_toping:Number((me=s==null?void 0:s.extra_data)==null?void 0:me.no_update_quantity_toping)||0,formula_qrcode:((ue=s==null?void 0:s.extra_data)==null?void 0:ue.formula_qrcode)||"",is_service:Number(s==null?void 0:s.is_service)||0,price_by_source:((he=s==null?void 0:s.extra_data)==null?void 0:he.price_by_source)||[],exclude_items_buffet:((xe=s==null?void 0:s.extra_data)==null?void 0:xe.exclude_items_buffet)||[],up_size_buffet:((_e=s==null?void 0:s.extra_data)==null?void 0:_e.up_size_buffet)||[],cross_price:((ge=s==null?void 0:s.extra_data)==null?void 0:ge.cross_price)||[],quantity:((fe=(je=(pe=s==null?void 0:s.extra_data)==null?void 0:pe.cross_price)==null?void 0:je[0])==null?void 0:fe.quantity)||0,price:((Ne=(be=(ve=s==null?void 0:s.extra_data)==null?void 0:ve.cross_price)==null?void 0:be[0])==null?void 0:Ne.price)||0,time_sale_date_week:Number(s==null?void 0:s.time_sale_date_week)||0,time_sale_hour_day:Number(s==null?void 0:s.time_sale_hour_day)||0,sort:Number(s==null?void 0:s.sort)||0,customization_uid:(s==null?void 0:s.customization_uid)||null,apply_with_store:Number(s==null?void 0:s.apply_with_store)||2,item_color:(s==null?void 0:s.item_color)||""}}),te=n=>{var U;const q=(U=n.target.files)==null?void 0:U[0];if(q){I(q);const Y=new FileReader;Y.onload=le=>{var se;y((se=le.target)==null?void 0:se.result)},Y.readAsDataURL(q)}},G=async n=>{if(!(!(_!=null&&_.id)||!(N!=null&&N.id)))try{let q="",U="",Y=n.item_color||"";if(S){const T=await Z(S);T&&(q=T.image_path,U=T.image_path_thumb,Y="")}else b&&!j&&(q=(s==null?void 0:s.image_path)||"",U=(s==null?void 0:s.image_path_thumb)||"");const le={price_by_source:(n.price_by_source||[]).map(T=>({price:Number(T.price??T.amount??0),source_id:String(T.source_id??T.source??T.sourceId??""),price_times:Array.isArray(T.price_times)?T.price_times:[],is_source_exist_in_city:T.is_source_exist_in_city??!0})),is_virtual_item:n.is_virtual_item,is_item_service:n.is_item_service,no_update_quantity_toping:n.no_update_quantity_toping,enable_edit_price:n.enable_edit_price,is_buffet_item:n.is_buffet_item,exclude_items_buffet:n.exclude_items_buffet||[],up_size_buffet:n.up_size_buffet||[],cross_price:n.cross_price||[],formula_qrcode:n.formula_qrcode||""},se=p?n.enable_custom_item_id&&n.item_id?n.item_id:(s==null?void 0:s.item_id)||(t==null?void 0:t.item_id)||"":j?n.enable_custom_item_id&&n.item_id&&n.item_id.trim()!==""?n.item_id:ce():n.enable_custom_item_id&&n.item_id?n.item_id:ce(),ae=(u||[]).find(T=>String(T.id)===String(n.store_uid)||String(T.store_uid)===String(n.store_uid)),we={item_id:se,item_name:n.item_name,description:n.description||"",ots_price:n.ots_price,ots_tax:n.ots_tax,ta_price:n.ta_price,ta_tax:n.ta_tax,time_sale_hour_day:n.time_sale_hour_day,time_sale_date_week:n.time_sale_date_week,allow_take_away:n.allow_take_away,is_eat_with:n.is_eat_with?1:0,image_path:q,image_path_thumb:U,item_color:Y,list_order:n.list_order,is_service:n.is_service?1:0,is_material:n.is_material,active:1,user_id:(s==null?void 0:s.user_id)||"",is_foreign:0,quantity_default:n.quantity_default,price_change:n.price_change,currency_type_id:(s==null?void 0:s.currency_type_id)||"",point:n.point,is_gift:n.is_gift,is_fc:n.is_fc,show_on_web:n.show_on_web,show_price_on_web:n.show_price_on_web,cost_price:n.cost_price,is_print_label:n.is_print_label?1:0,quantity_limit:n.quantity_limit,is_kit:n.is_kit,process_index:n.process_index,quantity_per_day:n.quantity_per_day,is_parent:n.is_parent,is_sub:n.is_sub,effective_date:n.effective_date,expire_date:n.expire_date,time_cooking:n.time_cooking,item_id_barcode:n.item_id_barcode||"",is_allow_discount:n.is_allow_discount?1:0,item_id_eat_with:(s==null?void 0:s.item_id_eat_with)||"",item_id_mapping:n.item_id_mapping||"",unit_uid:n.unit_uid,unit_secondary_uid:n.unit_secondary_uid||null,item_class_uid:n.item_class_uid||null,item_type_uid:n.item_type_uid,city_uid:(ae==null?void 0:ae.city_uid)||"",store_uid:n.store_uid||"",sort:n.sort,company_uid:_.id,brand_uid:N.id,sort_online:n.sort_online,customization_uid:n.customization_uid||null,is_fabi:1,apply_with_store:n.apply_with_store,revision:0,extra_data:le},ye={...p?{id:(t==null?void 0:t.id)||""}:{},...we};p&&(t!=null&&t.id)?(await w(ye),x({to:"/menu/items/items-in-store"})):(await l(ye),x({to:"/menu/items/items-in-store"}))}catch(q){console.error("Error submitting form:",q)}},ie=async()=>{if(!(!(t!=null&&t.id)||!(_!=null&&_.id)||!(N!=null&&N.id)))try{await Q({id:t.id,active:0}),x({to:"/menu/items/items-in-store"})}catch{}},D=async()=>{if(!(!(t!=null&&t.id)||!(_!=null&&_.id)||!(N!=null&&N.id)))try{await Q({id:t.id,active:1}),x({to:"/menu/items/items-in-store"})}catch{}},ne=async()=>{if(!p){const q=F.getValues("enable_custom_item_id"),U=F.getValues("item_id");!q&&(!U||String(U).trim()==="")&&F.setValue("item_id",ce(),{shouldDirty:!0})}await F.trigger()&&F.handleSubmit(G)()},R=n=>{const U=[...F.getValues("price_by_source")||[],{source_id:n.source_id,price:n.price,source_name:n.source_name,price_times:Array.isArray(n.price_times)?n.price_times:[],is_source_exist_in_city:n.is_source_exist_in_city??!0}];F.setValue("price_by_source",U,{shouldDirty:!0,shouldValidate:!0}),k(!1)},ee=a||X||W;return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(hs,{isUpdate:p,isCopy:j,currentRow:t,isLoading:ee,onSave:ne,onDeactive:p?ie:void 0,onActive:p?D:void 0,isDeactivating:J,isActivating:J}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx("div",{className:"bg-white",children:e.jsx(Me,{...F,children:e.jsx("form",{onSubmit:F.handleSubmit(G),className:"space-y-6",children:e.jsx(fs,{form:F,mode:p?"update":"create",itemTypes:M,itemClasses:A,units:K,stores:u,imageFile:S,onImageChange:te,imagePreview:b,onImageRemove:()=>{y(null),I(null)}})})})})})]}),e.jsx(Ce,{open:v,onOpenChange:k,onConfirm:R,sources:V})]})}export{it as I};
