import{r as x,R as de,j as e,B as j,a4 as V,h as pe,c as me,T as Me,o as _e,p as Re,q as ze}from"./index-DLSysrh4.js";import"./pos-api-CYJtTNYA.js";import"./vietqr-api-CdtuA3dS.js";import{u as Be}from"./use-customizations-CPiPqWYz.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";import{H as Oe}from"./header-BO8CjX_w.js";import{M as Fe}from"./main-W6TPZi-0.js";import{P as Ue}from"./profile-dropdown-D5DnkWo7.js";import{S as Ve,T as He}from"./search-CCOrl5KY.js";import{u as Y,a as ge,b as Pe,c as Ae,I as Le,d as Ke,e as Ee,f as $e,C as Xe,B as We}from"./customization-dialog-C_p9-QXm.js";import{B as qe,x as ae,G as le,h as Ge,H as Je,J as Qe,K as Ye,i as Ze,A as es,a as ss,C as ts}from"./react-icons.esm-tBZuCPpJ.js";import{D as as,a as ls,b as ns,c as X}from"./dropdown-menu-DxrvXCFj.js";import{read as fe,utils as je}from"./xlsx-DkH2s96g.js";import{u as ve}from"./use-item-types-CSsfT6lm.js";import{u as Ne}from"./use-removed-items-Do_IGH04.js";import{D as ne,a as ie,b as ce,c as oe}from"./dialog-DGVckEWq.js";import{S as W,a as q,b as G,c as J,d as O}from"./select-LfFpGbbb.js";import{u as we,e as Ce,f as se,g as is,a as cs,b as os,d as rs}from"./index-DLl1pUb8.js";import{T as be,a as ye,b as Q,c as Se,d as Ie,e as te}from"./table-yR0osUgs.js";import{D as U}from"./data-table-column-header-CSFWyMBD.js";import{B as he}from"./badge-CSxw3ZSI.js";import{S as ds}from"./status-badge-EB_R6QG4.js";import"./date-range-picker-CoReVTN3.js";import"./form-DCttU9FS.js";import{C as xe}from"./checkbox-C0erTYQj.js";import{S as ee}from"./settings-DeXBUVW7.js";import{I as ms}from"./IconCopy-CuNrJfmX.js";import{I as hs}from"./IconTrash-D-NyrCHn.js";import{S as xs,a as us}from"./scroll-area-DCgoUvjl.js";import{C as Te}from"./confirm-dialog-DflbPhcO.js";import{a as ps,C as gs}from"./chevron-right-CzKIkqDA.js";import{I as fs}from"./input-42MGwRgA.js";import{M as js}from"./multi-select-DB8GW7bS.js";import{T as vs}from"./trash-2-C1HJm0c9.js";import{I as Ns}from"./IconFilter-BPp9hAKZ.js";import{X as ws}from"./calendar-DaAHj6Vo.js";import{S as o}from"./skeleton-BP3IiuyP.js";import"./useQuery-w3l-JMwb.js";import"./utils-km2FGkQ4.js";import"./useMutation-DGZ6hM-V.js";import"./query-keys-xK68hCS0.js";import"./separator-DsrkJotP.js";import"./avatar-C2xen_AP.js";import"./search-context-GGIHanUV.js";import"./command-WbkkLvMj.js";import"./search-CX_1dbj5.js";import"./createLucideIcon-BCCeA4NW.js";import"./createReactComponent-CV7I4pf9.js";import"./IconChevronRight-BwCnOkVS.js";import"./IconSearch-K-wA4kRr.js";import"./use-dialog-state-BCMBa9nj.js";import"./use-item-classes-DGMAg27S.js";import"./use-units-gLxkZ0xs.js";import"./modal-BEyJXC3x.js";import"./zod-j2m7p1zB.js";import"./combobox-Cw6fZwGg.js";import"./popover-BVjXu8gX.js";import"./index-BmCCDDB3.js";import"./chevrons-up-down-BkQP1fiS.js";import"./check-B956xcJg.js";import"./index-BvxNbdt9.js";import"./index-QWmA5vou.js";import"./index-D_lRkZQY.js";import"./isSameMonth-C8JQo-AN.js";import"./alert-dialog-jC5TDKEJ.js";import"./circle-x-DosBFIxj.js";function Cs(){const[i,s]=x.useState(!1),[a,c]=x.useState(!1),[t,r]=x.useState(null),[d,f]=x.useState(!1),[p,D]=x.useState([]),[I,b]=x.useState("all"),[T,h]=x.useState("all"),[m,u]=x.useState([]),[v,C]=x.useState("all");return{isCustomizationDialogOpen:i,isBuffetItem:a,selectedMenuItem:t,isBuffetConfigModalOpen:d,selectedBuffetMenuItem:p,selectedItemTypeUid:I,selectedCityUid:T,selectedDaysOfWeek:m,selectedStatus:v,setIsCustomizationDialogOpen:s,setIsBuffetItem:c,setSelectedMenuItem:r,setIsBuffetConfigModalOpen:f,setSelectedBuffetMenuItem:D,setSelectedItemTypeUid:b,setSelectedCityUid:h,setSelectedDaysOfWeek:u,setSelectedStatus:C}}function ke({open:i,onOpenChange:s,data:a,onSave:c}){var T;const[t,r]=x.useState(a);de.useEffect(()=>{r(a)},[a]);const d=()=>{c(t),V.success("Data saved successfully"),s(!1)},f=()=>{s(!1)},p=de.useCallback(h=>{const m=t.filter((u,v)=>v!==h);r(m)},[t]),{tableData:D,columns:I}=x.useMemo(()=>{if(!t||t.length===0)return{tableData:[],columns:[]};const h=t[0]||[],m=t.slice(1),u=[{id:"actions",header:"-",cell:({row:C})=>e.jsx(j,{variant:"ghost",size:"sm",onClick:()=>p(C.original._originalIndex),className:"h-6 w-6 p-0 text-red-500 hover:text-red-700",children:e.jsx(qe,{className:"h-4 w-4"})}),enableSorting:!1,enableHiding:!1,size:50,meta:{className:"w-12 text-center sticky left-0 bg-background z-20 border-r"}},...h.map((C,g)=>({id:`col_${g}`,accessorKey:`col_${g}`,header:String(C),cell:({row:y})=>e.jsx("div",{className:"min-w-[150px] whitespace-nowrap",children:y.getValue(`col_${g}`)}),enableSorting:!1,enableHiding:!1,meta:{className:"min-w-[150px] px-4 whitespace-nowrap"}}))];return{tableData:m.map((C,g)=>{const y={_originalIndex:g+1};return C.forEach((l,M)=>{y[`col_${M}`]=l}),y}),columns:u}},[t,p]),b=we({data:D,columns:I,getCoreRowModel:Ce()});return!t||t.length===0?null:e.jsx(ne,{open:i,onOpenChange:s,children:e.jsx(ie,{className:"h-[525px] w-[1140px] !max-w-[1140px] overflow-hidden p-0",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx(ce,{className:"shrink-0 border-b px-6 py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(oe,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(j,{variant:"outline",size:"sm",onClick:f,children:"Đóng"}),e.jsx(j,{size:"sm",className:"bg-green-600 hover:bg-green-700",onClick:d,children:"Lưu"})]})]})}),e.jsx("div",{className:"flex-1 overflow-hidden p-6",children:e.jsx("div",{className:"bg-background h-full w-full overflow-auto rounded-lg border",children:e.jsx("div",{className:"min-w-max",children:e.jsxs(be,{children:[e.jsx(ye,{className:"bg-muted/50 sticky top-0 z-10",children:b.getHeaderGroups().map(h=>e.jsx(Q,{children:h.headers.map(m=>{var u;return e.jsx(Se,{className:((u=m.column.columnDef.meta)==null?void 0:u.className)||"",children:m.isPlaceholder?null:se(m.column.columnDef.header,m.getContext())},m.id)})},h.id))}),e.jsx(Ie,{children:(T=b.getRowModel().rows)!=null&&T.length?b.getRowModel().rows.map(h=>e.jsx(Q,{className:"hover:bg-muted/50",children:h.getVisibleCells().map(m=>{var u;return e.jsx(te,{className:((u=m.column.columnDef.meta)==null?void 0:u.className)||"",children:se(m.column.columnDef.cell,m.getContext())},m.id)})},h.id)):e.jsx(Q,{children:e.jsx(te,{colSpan:I.length,className:"h-24 text-center",children:"No data."})})})]})})})})]})})})}function bs(){var F,A,L;const{open:i,setOpen:s}=Y(),[a,c]=x.useState("all"),[t,r]=x.useState("all"),[d,f]=x.useState("all"),[p,D]=x.useState([]),[I,b]=x.useState(!1),T=x.useRef(null),{data:h=[]}=ve(),{data:m=[]}=Ne(),{downloadTemplateAsync:u,isPending:v}=ge(),C=h.filter(n=>n.active===1).map(n=>({label:n.item_type_name,value:n.id})),g=m.filter(n=>n.active===1).map(n=>({label:n.city_name,value:n.id})),y=[{label:"Active",value:"1"},{label:"Deactive",value:"0"}],l=async()=>{try{const n=await u({city_uid:a!=="all"?a:void 0,item_type_uid:t!=="all"?t:void 0,active:d!=="all"?d:void 0}),_=window.URL.createObjectURL(n),k=document.createElement("a");k.href=_,k.download=`danh-sach-mon-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(k),k.click(),document.body.removeChild(k),window.URL.revokeObjectURL(_),V.success("Tải xuống template thành công!")}catch{V.error("Lỗi khi tải template")}},M=n=>{var K;const _=(K=n.target.files)==null?void 0:K[0];if(!_)return;const k=new FileReader;k.onload=E=>{var $;try{const R=new Uint8Array(($=E.target)==null?void 0:$.result),P=fe(R,{type:"array"}),S=P.SheetNames[0],w=P.Sheets[S],z=je.sheet_to_json(w,{header:1});z.length>0&&(D(z),s(null),b(!0),V.success("File uploaded successfully"))}catch{V.error("Error parsing file")}},k.readAsArrayBuffer(_)},B=()=>{var n;(n=T.current)==null||n.click()},H=n=>{s(null),window.location.reload()};return e.jsxs(e.Fragment,{children:[e.jsx(ne,{open:i==="export",onOpenChange:n=>s(n?"export":null),children:e.jsxs(ie,{className:"max-w-2xl",children:[e.jsx(ce,{children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(oe,{className:"text-xl font-semibold",children:"Xuất, sửa thực đơn"})})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Chỉnh bộ lọc để xuất file"}),e.jsxs("div",{className:"flex flex-wrap gap-4",children:[e.jsxs(W,{value:a,onValueChange:c,children:[e.jsx(q,{className:"w-[180px] min-w-[160px]",children:e.jsx(G,{children:a==="all"?"Tất cả thành phố":((F=g.find(n=>n.value===a))==null?void 0:F.label)||"Tất cả thành phố"})}),e.jsxs(J,{children:[e.jsx(O,{value:"all",children:"Tất cả thành phố"}),g.map(n=>e.jsx(O,{value:n.value,children:n.label},n.value))]})]}),e.jsxs(W,{value:t,onValueChange:r,children:[e.jsx(q,{className:"w-[180px] min-w-[160px]",children:e.jsx(G,{children:t==="all"?"Tất cả nhóm món":((A=C.find(n=>n.value===t))==null?void 0:A.label)||"Tất cả nhóm món"})}),e.jsxs(J,{children:[e.jsx(O,{value:"all",children:"Tất cả nhóm món"}),C.map(n=>e.jsx(O,{value:n.value,children:n.label},n.value))]})]}),e.jsxs(W,{value:d,onValueChange:f,children:[e.jsx(q,{className:"w-[180px] min-w-[160px]",children:e.jsx(G,{children:d==="all"?"Tất cả trạng thái":((L=y.find(n=>n.value===d))==null?void 0:L.label)||"Tất cả trạng thái"})}),e.jsxs(J,{children:[e.jsx(O,{value:"all",children:"Tất cả trạng thái"}),y.map(n=>e.jsx(O,{value:n.value,children:n.label},n.value))]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Tải file dữ liệu"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Tải xuống"}),e.jsxs(j,{variant:"outline",size:"sm",onClick:l,disabled:v,className:"flex items-center gap-2",children:[e.jsx(ae,{className:"h-4 w-4"}),v&&"Đang tải..."]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Thêm cấu hình vào file"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-muted-foreground text-sm",children:"Không sửa các cột :"}),e.jsx("p",{className:"font-mono text-sm text-blue-600",children:"ID, Mã món, Thành phố, Đơn vị, Tên nhóm, Tên loại."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 4. Tải file lên"}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-muted-foreground text-sm",children:"Sau khi đã điền đầy đủ bạn có thể tải file lên"}),e.jsxs(j,{variant:"outline",size:"sm",onClick:B,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(le,{className:"h-4 w-4"})]}),e.jsx("input",{ref:T,type:"file",accept:".xlsx,.xls",onChange:M,style:{display:"none"}})]})]})]})]})}),e.jsx(ke,{open:I,onOpenChange:b,data:p,onSave:H})]})}function ys(){const{open:i,setOpen:s}=Y(),[a,c]=x.useState(!1),[t,r]=x.useState([]),d=x.useRef(null),{downloadTemplateAsync:f,isPending:p}=ge(),D=async()=>{try{const h=await f({city_uid:"all",item_type_uid:"all",active:"all"}),m=window.URL.createObjectURL(h),u=document.createElement("a");u.href=m,u.download=`items-template-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(m),V.success("Tải template thành công")}catch{V.error("Lỗi khi tải template")}},I=()=>{var h;(h=d.current)==null||h.click()},b=h=>{var v;const m=(v=h.target.files)==null?void 0:v[0];if(!m)return;const u=new FileReader;u.onload=C=>{var g;try{const y=new Uint8Array((g=C.target)==null?void 0:g.result),l=fe(y,{type:"array"}),M=l.SheetNames[0],B=l.Sheets[M],H=je.sheet_to_json(B,{header:1,defval:"",raw:!1});if(H.length===0){V.error("File không có dữ liệu");return}r(H),s(null),c(!0),d.current&&(d.current.value="")}catch{V.error("Lỗi khi đọc file. Vui lòng kiểm tra định dạng file.")}},u.readAsArrayBuffer(m)},T=()=>{V.success("Dữ liệu đã được lưu thành công!"),c(!1),s(null)};return e.jsxs(e.Fragment,{children:[e.jsx(ne,{open:i==="import",onOpenChange:h=>s(h?"import":null),children:e.jsxs(ie,{className:"max-w-2xl",children:[e.jsx(ce,{children:e.jsx(oe,{children:"Thêm món"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 1. Tải file mẫu"}),e.jsx(j,{variant:"outline",size:"sm",onClick:D,disabled:p,className:"flex items-center gap-2",children:p?"Đang tải...":e.jsxs(e.Fragment,{children:["Tải xuống",e.jsx(ae,{className:"h-4 w-4"})]})})]})}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 2. Thêm món vào file"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("p",{children:["Không được để trống các cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Tên, Thành phố"}),"."]}),e.jsxs("p",{children:["Các cột còn lại có thể để trống, để gán nhóm, loại, đơn vị cho món: Nhập mã nhóm, mã loại, mã đơn vị đã có vào cột ",e.jsx("span",{className:"font-mono text-blue-600",children:"Nhóm, Loại món"}),"."]}),e.jsxs("p",{children:["Mã đơn vị món, mã thành phố có thể xem trong sheet"," ",e.jsx("span",{className:"font-mono text-blue-600",children:"Guide"})," của file mẫu."]})]})]}),e.jsx("div",{className:"space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium",children:"Bước 3. Tải file thực đơn lên"}),e.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Sau khi đã điền đầy đủ thực đơn bạn có thể tải file lên"})]}),e.jsxs(j,{variant:"outline",size:"sm",onClick:I,className:"flex items-center gap-2",children:["Tải file lên",e.jsx(le,{className:"h-4 w-4"})]})]})})]})]})}),e.jsx("input",{ref:d,type:"file",accept:".xlsx,.xls",onChange:b,style:{display:"none"}}),e.jsx(ke,{open:a,onOpenChange:c,data:t,onSave:T})]})}function Ss(){const{setOpen:i}=Y(),s=pe(),a=()=>{s({to:"/menu/items/items-in-city/detail"})},c=()=>{i("export")},t=()=>{i("import")},r=()=>{},d=()=>{},f=()=>{},p=()=>{};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(as,{children:[e.jsx(ls,{asChild:!0,children:e.jsxs(j,{variant:"outline",size:"sm",children:["Tiện ích",e.jsx(Ge,{className:"ml-2 h-4 w-4"})]})}),e.jsxs(ns,{align:"end",className:"w-56",children:[e.jsxs(X,{onClick:c,children:[e.jsx(ae,{className:"mr-2 h-4 w-4"}),"Xuất, sửa thực đơn"]}),e.jsxs(X,{onClick:t,children:[e.jsx(le,{className:"mr-2 h-4 w-4"}),"Thêm món từ file"]}),e.jsxs(X,{onClick:r,children:[e.jsx(Je,{className:"mr-2 h-4 w-4"}),"Cấu hình giá theo nguồn"]}),e.jsxs(X,{onClick:d,children:[e.jsx(Qe,{className:"mr-2 h-4 w-4"}),"Sắp xếp thực đơn"]}),e.jsxs(X,{onClick:f,children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),"Sao chép thực đơn"]}),e.jsxs(X,{onClick:p,children:[e.jsx(Ze,{className:"mr-2 h-4 w-4"}),"Cấu hình khung thời gian"]})]})]}),e.jsx(j,{variant:"default",size:"sm",onClick:a,children:"Tạo món"})]}),e.jsx(bs,{}),e.jsx(ys,{})]})}function ue({column:i,title:s,className:a,defaultSort:c="desc"}){if(!i.getCanSort())return e.jsx("div",{className:me(a),children:s});const t=()=>{const r=i.getIsSorted();r?r==="desc"?i.toggleSorting(!1):i.toggleSorting(!0):i.toggleSorting(c==="desc")};return e.jsx("div",{className:me("flex items-center space-x-2",a),children:e.jsxs(j,{variant:"ghost",size:"sm",className:"-ml-3 h-8 hover:bg-accent",onClick:t,children:[e.jsx("span",{children:s}),i.getIsSorted()==="desc"?e.jsx(es,{className:"ml-2 h-4 w-4"}):i.getIsSorted()==="asc"?e.jsx(ss,{className:"ml-2 h-4 w-4"}):e.jsx(ts,{className:"ml-2 h-4 w-4"})]})})}const Is=({onBuffetConfigClick:i})=>[{id:"select",header:({table:s})=>e.jsx(xe,{checked:s.getIsAllPageRowsSelected(),onCheckedChange:a=>s.toggleAllPageRowsSelected(!!a),"aria-label":"Select all"}),cell:({row:s})=>e.jsx(xe,{checked:s.getIsSelected(),onCheckedChange:a=>s.toggleSelected(!!a),"aria-label":"Select row",onClick:a=>a.stopPropagation()}),enableSorting:!1,enableHiding:!1,size:50},{id:"index",header:"#",cell:({row:s})=>e.jsx("div",{className:"w-[50px]",children:s.index+1}),enableSorting:!1,enableHiding:!1,size:50},{accessorKey:"code",header:({column:s})=>e.jsx(U,{column:s,title:"Mã món"}),cell:({row:s})=>e.jsx("div",{className:"text-sm font-medium",children:s.getValue("code")}),enableSorting:!1,enableHiding:!0},{accessorKey:"name",header:({column:s})=>e.jsx(U,{column:s,title:"Tên món"}),cell:({row:s})=>e.jsx("div",{className:"max-w-[200px] truncate text-sm font-medium",children:s.getValue("name")}),enableSorting:!1,enableHiding:!0},{accessorKey:"price",header:({column:s})=>e.jsx(U,{column:s,title:"Giá"}),cell:({row:s})=>{const a=s.getValue("price");return e.jsx("div",{className:"text-sm font-medium",children:new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(a)})},enableSorting:!1,enableHiding:!0},{accessorKey:"vatPercent",header:({column:s})=>e.jsx(U,{column:s,title:"VAT (%)"}),cell:({row:s})=>{const a=s.getValue("vatPercent");return e.jsx("div",{className:"text-right text-sm",children:a*100})},enableSorting:!1,enableHiding:!0},{accessorKey:"itemType",header:({column:s})=>e.jsx(U,{column:s,title:"Nhóm món"}),cell:({row:s})=>e.jsx(he,{variant:"outline",className:"text-xs",children:s.getValue("itemType")}),enableSorting:!1,enableHiding:!0},{accessorKey:"itemClass",header:({column:s})=>e.jsx(U,{column:s,title:"Loại món"}),cell:({row:s})=>s.getValue("itemClass")&&e.jsx(he,{variant:"outline",className:"text-center text-xs",children:s.getValue("itemClass")}),enableSorting:!1,enableHiding:!0},{accessorKey:"unit",header:({column:s})=>e.jsx(U,{column:s,title:"Đơn vị tính"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("unit")}),enableSorting:!1,enableHiding:!0},{accessorKey:"sideItems",header:({column:s})=>e.jsx(ue,{column:s,title:"Món ăn kèm",defaultSort:"desc"}),cell:({row:s})=>{const a=s.getValue("sideItems");if(!a)return e.jsx("div",{children:"Món chính"});const c=a==="Món ăn kèm"?"Món ăn kèm":a;return e.jsx(Me,{children:e.jsxs(_e,{children:[e.jsx(Re,{asChild:!0,children:e.jsx("div",{className:"max-w-[120px] cursor-help truncate text-sm",children:c})}),e.jsx(ze,{children:e.jsx("p",{className:"max-w-[300px]",children:c})})]})})},enableSorting:!0,enableHiding:!0},{accessorKey:"city",header:({column:s})=>e.jsx(U,{column:s,title:"Thành phố"}),cell:({row:s})=>e.jsx("div",{className:"text-sm",children:s.getValue("city")}),enableSorting:!1,enableHiding:!0},{accessorKey:"buffetConfig",header:({column:s})=>e.jsx(U,{column:s,title:"Cấu hình buffet"}),cell:({row:s})=>{var t;const a=s.original;return((t=a.extra_data)==null?void 0:t.is_buffet_item)===1?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:"Đã cấu hình"}),e.jsx(j,{variant:"outline",size:"sm",onClick:()=>i(a),className:"h-6 px-2 text-xs",children:e.jsx(ee,{className:"h-3 w-3"})})]}):e.jsxs(j,{variant:"outline",size:"sm",onClick:()=>i(a),className:"h-7 px-2 text-xs",children:[e.jsx(ee,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{accessorKey:"customization",header:({column:s})=>e.jsx(U,{column:s,title:"Customization"}),cell:({row:s,table:a})=>{var f;const c=s.original,t=a.options.meta,r=c.customization_uid,d=(f=t==null?void 0:t.customizations)==null?void 0:f.find(p=>p.id===r);return d?e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium",children:d.name}),e.jsx(j,{variant:"outline",size:"sm",onClick:()=>{var p;return(p=t==null?void 0:t.onCustomizationClick)==null?void 0:p.call(t,c)},className:"h-6 px-2 text-xs",children:e.jsx(ee,{className:"h-3 w-3"})})]}):e.jsxs(j,{variant:"outline",size:"sm",onClick:()=>{var p;return(p=t==null?void 0:t.onCustomizationClick)==null?void 0:p.call(t,c)},className:"h-7 px-2 text-xs",children:[e.jsx(ee,{className:"mr-1 h-3 w-3"}),"Cấu hình"]})},enableSorting:!1,enableHiding:!0},{id:"copy",header:"Sao chép tạo món mới",cell:({row:s,table:a})=>{const c=s.original,t=a.options.meta;return e.jsxs(j,{variant:"ghost",size:"sm",className:"ml-14 h-8 w-8",onClick:r=>{var d;r.stopPropagation(),(d=t==null?void 0:t.onCopyClick)==null||d.call(t,c)},children:[e.jsx(ms,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Sao chép thiết bị ",c.item_name]})]})},enableSorting:!1,enableHiding:!0},{accessorKey:"isActive",header:({column:s})=>e.jsx(ue,{column:s,title:"Thao tác",defaultSort:"desc"}),enableSorting:!0,cell:({row:s,table:a})=>{const c=s.original,t=s.getValue("isActive"),r=a.options.meta;return e.jsx("div",{onClick:d=>{var f;d.stopPropagation(),(f=r==null?void 0:r.onToggleStatus)==null||f.call(r,c)},className:"cursor-pointer",children:e.jsx(ds,{isActive:t,activeText:"Active",inactiveText:"Deactive"})})},enableHiding:!0},{id:"actions",cell:({row:s,table:a})=>{const c=s.original,t=a.options.meta;return e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(j,{variant:"ghost",size:"sm",onClick:r=>{var d;r.stopPropagation(),(d=t==null?void 0:t.onDeleteClick)==null||d.call(t,c)},className:"h-8 w-8 p-0 text-red-600 hover:text-red-700",children:[e.jsx(hs,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Xóa món ",c.item_name]})]})})},enableSorting:!1,enableHiding:!1,size:80}],Ts=Is;function ks({currentPage:i,onPageChange:s,hasNextPage:a}){const c=()=>{i>1&&s(i-1)},t=()=>{a&&s(i+1)};return e.jsxs("div",{className:"flex items-center justify-center gap-4 py-4",children:[e.jsxs(j,{variant:"outline",size:"sm",onClick:c,disabled:i===1,className:"flex items-center gap-2",children:[e.jsx(ps,{className:"h-4 w-4"}),"Trước"]}),e.jsx("span",{className:"text-sm font-medium",children:i}),e.jsxs(j,{variant:"outline",size:"sm",onClick:t,disabled:!a,className:"flex items-center gap-2",children:["Sau",e.jsx(gs,{className:"h-4 w-4"})]})]})}const Ds=[{label:"Thứ 2",value:"2"},{label:"Thứ 3",value:"3"},{label:"Thứ 4",value:"4"},{label:"Thứ 5",value:"5"},{label:"Thứ 6",value:"6"},{label:"Thứ 7",value:"7"},{label:"Chủ Nhật",value:"1"}],Ms=[{label:"Tất cả trạng thái",value:"all"},{label:"Active",value:"1"},{label:"Deactive",value:"0"}];function _s({table:i,selectedItemTypeUid:s="all",onItemTypeChange:a,selectedCityUid:c="all",onCityChange:t,selectedDaysOfWeek:r=[],onDaysOfWeekChange:d,selectedStatus:f="all",onStatusChange:p,onDeleteSelected:D}){var y;const[I,b]=x.useState(!1),{data:T=[]}=ve(),{data:h=[]}=Ne(),m=h.filter(l=>l.active===1),u=m.map(l=>({label:l.city_name,value:l.id})),v=m.map(l=>l.id).join(",");x.useEffect(()=>{c==="all"&&v&&t&&t(v)},[c,v,t]);const C=i.getState().columnFilters.length>0,g=i.getFilteredSelectedRowModel().rows.length;return e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex flex-1 items-center space-x-2",children:[g>0&&e.jsxs(j,{variant:"destructive",size:"sm",onClick:D,className:"h-9",children:[e.jsx(vs,{}),"Xóa món (",g,")"]}),e.jsx(fs,{placeholder:"Tìm kiếm món ăn...",value:((y=i.getColumn("name"))==null?void 0:y.getFilterValue())??"",onChange:l=>{var M;return(M=i.getColumn("name"))==null?void 0:M.setFilterValue(l.target.value)},className:"h-9 w-[150px] lg:w-[250px]"}),e.jsxs(W,{value:c,onValueChange:l=>{t&&t(l)},children:[e.jsx(q,{className:"h-10 w-[180px]",children:e.jsx(G,{placeholder:"Chọn thành phố"})}),e.jsxs(J,{children:[e.jsx(O,{value:v,children:"Tất cả thành phố"}),u.map(l=>e.jsx(O,{value:l.value,children:l.label},l.value))]})]}),e.jsxs(W,{value:f,onValueChange:p,children:[e.jsx(q,{className:"h-10 w-[180px]",children:e.jsx(G,{placeholder:"Chọn Trạng thái"})}),e.jsx(J,{children:Ms.map(l=>e.jsx(O,{value:l.value,children:l.label},l.value))})]}),e.jsxs(j,{variant:"outline",size:"sm",onClick:()=>b(!I),className:"h-9",children:[e.jsx(Ns,{className:"h-4 w-4"}),"Nâng cao"]}),C&&e.jsxs(j,{variant:"ghost",onClick:()=>i.resetColumnFilters(),className:"h-10 px-2 lg:px-3",children:["Reset",e.jsx(ws,{className:"ml-2 h-4 w-4"})]})]})}),I&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(W,{value:s,onValueChange:a,children:[e.jsx(q,{className:"h-10 w-[180px]",children:e.jsx(G,{placeholder:"Chọn loại món"})}),e.jsxs(J,{children:[e.jsx(O,{value:"all",children:"Tất cả nhóm món"}),T.filter(l=>l.active===1).map(l=>({label:l.item_type_name,value:l.id})).map(l=>e.jsx(O,{value:l.value,children:l.label},l.value))]})]}),e.jsx(js,{options:Ds,value:r,onValueChange:d||(()=>{}),placeholder:"Chọn ngày trong tuần",className:"min-h-9 w-[300px]",maxCount:1})]})]})}function Rs({columns:i,data:s,onCustomizationClick:a,onCopyClick:c,onToggleStatus:t,onRowClick:r,onDeleteClick:d,customizations:f,selectedItemTypeUid:p,onItemTypeChange:D,selectedCityUid:I,onCityChange:b,selectedDaysOfWeek:T,onDaysOfWeekChange:h,selectedStatus:m,onStatusChange:u,hasNextPageOverride:v,currentPage:C,onPageChange:g}){var P;const[y,l]=x.useState({}),[M,B]=x.useState({}),[H,F]=x.useState([]),[A,L]=x.useState([]),[n,_]=x.useState(!1),{deleteMultipleItemsAsync:k}=Pe(),K=()=>{_(!0)},E=async()=>{try{const w=R.getFilteredSelectedRowModel().rows.map(z=>z.original.id);await k(w),_(!1),R.resetRowSelection()}catch{}},$=(S,w)=>{const z=w.target;z.closest('input[type="checkbox"]')||z.closest("button")||z.closest('[role="button"]')||z.closest(".badge")||z.tagName==="BUTTON"||r==null||r(S)},R=we({data:s,columns:i,state:{sorting:A,columnVisibility:M,rowSelection:y,columnFilters:H},enableRowSelection:!0,onRowSelectionChange:l,onSortingChange:L,onColumnFiltersChange:F,onColumnVisibilityChange:B,getCoreRowModel:Ce(),getFilteredRowModel:rs(),getSortedRowModel:os(),getFacetedRowModel:cs(),getFacetedUniqueValues:is(),meta:{onCustomizationClick:a,onCopyClick:c,onToggleStatus:t,onDeleteClick:d,customizations:f}});return e.jsxs("div",{className:"space-y-4",children:[e.jsx(_s,{table:R,selectedItemTypeUid:p,onItemTypeChange:D,selectedCityUid:I,onCityChange:b,selectedDaysOfWeek:T,onDaysOfWeekChange:h,selectedStatus:m,onStatusChange:u,onDeleteSelected:K}),e.jsxs(xs,{className:"rounded-md border",children:[e.jsxs(be,{className:"relative",children:[e.jsx(ye,{children:R.getHeaderGroups().map(S=>e.jsx(Q,{children:S.headers.map(w=>e.jsx(Se,{colSpan:w.colSpan,children:w.isPlaceholder?null:se(w.column.columnDef.header,w.getContext())},w.id))},S.id))}),e.jsx(Ie,{children:(P=R.getRowModel().rows)!=null&&P.length?R.getRowModel().rows.map(S=>e.jsx(Q,{"data-state":S.getIsSelected()&&"selected",className:"hover:bg-muted/50 cursor-pointer",onClick:w=>$(S.original,w),children:S.getVisibleCells().map(w=>e.jsx(te,{children:se(w.column.columnDef.cell,w.getContext())},w.id))},S.id)):e.jsx(Q,{children:e.jsx(te,{colSpan:i.length,className:"h-24 text-center",children:"Không có dữ liệu."})})})]}),e.jsx(us,{orientation:"horizontal"})]}),e.jsx(ks,{currentPage:C??1,onPageChange:S=>g&&g(S),hasNextPage:!!v}),e.jsx(Te,{open:n,onOpenChange:_,title:`Bạn có chắc muốn xóa ${R.getFilteredSelectedRowModel().rows.length} món đã chọn`,desc:"Hành động này không thể hoàn tác.",confirmText:"Xóa",cancelBtnText:"Hủy",className:"top-[30%] translate-y-[-50%]",handleConfirm:E,destructive:!0})]})}function zs(){return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-[250px]"}),e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-[100px]"})]}),e.jsx(o,{className:"h-8 w-[100px]"})]}),e.jsxs("div",{className:"rounded-md border",children:[e.jsx("div",{className:"border-b p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(o,{className:"h-4 w-8"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-32"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-16"})]})}),Array.from({length:10}).map((i,s)=>e.jsx("div",{className:"border-b p-4 last:border-b-0",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(o,{className:"h-4 w-8"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-32"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-16"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-20"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-24"}),e.jsx(o,{className:"h-4 w-16"})]})},s))]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(o,{className:"h-8 w-[200px]"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(o,{className:"h-8 w-[100px]"}),e.jsx(o,{className:"h-8 w-8"}),e.jsx(o,{className:"h-8 w-8"})]})]})]})}function Bs(){const{open:i,setOpen:s,currentRow:a,setCurrentRow:c}=Y(),{deleteItemAsync:t}=Ae();return e.jsx(e.Fragment,{children:a&&e.jsx(e.Fragment,{children:e.jsx(Te,{destructive:!0,open:i==="delete",onOpenChange:r=>{r||(s(null),setTimeout(()=>{c(null)},500))},handleConfirm:async()=>{s(null),setTimeout(()=>{c(null)},500),await t(a.id||"")},className:"max-w-md",title:"Bạn có muốn xoá ?",desc:e.jsx(e.Fragment,{children:"Hành động không thể hoàn tác."}),confirmText:"Xoá"},"quantity-day-delete")})})}function Os(){const i=pe(),[s,a]=x.useState(1),{open:c,setOpen:t,setCurrentRow:r}=Y(),{updateStatusAsync:d}=Ke(),{updateItemAsync:f}=Ee(),{isCustomizationDialogOpen:p,isBuffetItem:D,isBuffetConfigModalOpen:I,setIsCustomizationDialogOpen:b,setIsBuffetItem:T,selectedMenuItem:h,setSelectedMenuItem:m,setIsBuffetConfigModalOpen:u,selectedBuffetMenuItem:v,setSelectedBuffetMenuItem:C,selectedItemTypeUid:g,setSelectedItemTypeUid:y,selectedCityUid:l,setSelectedCityUid:M,selectedDaysOfWeek:B,setSelectedDaysOfWeek:H,selectedStatus:F,setSelectedStatus:A}=Cs(),L=x.useMemo(()=>({...g!=="all"&&{item_type_uid:g},...l!=="all"&&{city_uid:l},...B.length>0&&{time_sale_date_week:B.join(",")},...F!=="all"&&{active:parseInt(F,10)},page:s}),[g,l,B,F,s]);x.useEffect(()=>{a(1)},[g,l,B,F]);const{data:n=[],isLoading:_,error:k,hasNextPage:K}=$e({params:L}),{data:E=[]}=Be({skip_limit:!0,list_city_uid:l!=="all"?[l]:void 0}),$=N=>{m(N),b(!0)},R=N=>{var Z,re;m(N),C(((Z=N==null?void 0:N.extra_data)==null?void 0:Z.exclude_items_buffet)||[]),T(((re=N==null?void 0:N.extra_data)==null?void 0:re.is_buffet_item)===1),u(!0)},P=N=>{i({to:"/menu/items/items-in-city/detail",search:{id:N.id||""}})},S=N=>{r(N),t("delete")},w=N=>{i({to:"/menu/items/items-in-city/detail/$id",params:{id:N.id||""}})},z=async N=>{const Z=N.active?0:1;await d({id:N.id||"",active:Z})},De=Ts({onBuffetConfigClick:R});return k?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("p",{className:"text-muted-foreground mb-2 text-sm",children:"Có lỗi xảy ra khi tải dữ liệu"}),e.jsx("p",{className:"text-muted-foreground text-xs",children:k&&`Món ăn: ${(k==null?void 0:k.message)||"Lỗi không xác định"}`})]})}):e.jsxs(e.Fragment,{children:[!c&&e.jsxs(e.Fragment,{children:[e.jsx(Oe,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Ve,{}),e.jsx(He,{}),e.jsx(Ue,{})]})}),e.jsxs(Fe,{children:[e.jsxs("div",{className:"mb-2 flex flex-wrap items-center justify-between space-y-2 gap-x-4",children:[e.jsx("div",{children:e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Món ăn tại thành phố"})}),e.jsx(Ss,{})]}),e.jsxs("div",{className:"-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12",children:[_&&e.jsx(zs,{}),!_&&e.jsx(Rs,{columns:De,data:n,onCustomizationClick:$,onCopyClick:P,onToggleStatus:z,onRowClick:w,onDeleteClick:S,customizations:E,selectedItemTypeUid:g,onItemTypeChange:y,selectedCityUid:l,onCityChange:M,selectedDaysOfWeek:B,onDaysOfWeekChange:H,selectedStatus:F,onStatusChange:A,hasNextPageOverride:K,currentPage:s,onPageChange:a})]})]})]}),e.jsx(Bs,{}),p&&h&&e.jsx(Xe,{open:p,onOpenChange:b,item:h,customizations:E}),I&&v&&e.jsx(We,{itemsBuffet:v,open:I,onOpenChange:u,onItemsChange:async N=>{await f({...h,extra_data:{is_buffet_item:D?1:0,exclude_items_buffet:N}})},items:n,hide:!1,enable:D,onEnableChange:T})]})}function Fs(){return e.jsx(Le,{children:e.jsx(Os,{})})}const Jt=Fs;export{Jt as component};
