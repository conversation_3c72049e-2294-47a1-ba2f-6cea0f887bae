import{j as e,r as p,u as q,a4 as T,B as v,T as Q,o as Z,p as ee,q as te,h as z,R as ne}from"./index-DLSysrh4.js";import{g as se}from"./error-utils-BtHHKnij.js";import{H as M}from"./header-BO8CjX_w.js";import{M as k}from"./main-W6TPZi-0.js";import{P as I}from"./profile-dropdown-D5DnkWo7.js";import{S as L,T as E}from"./search-CCOrl5KY.js";import"./date-range-picker-CoReVTN3.js";import"./form-DCttU9FS.js";import{S as C,a as U}from"./revenue-lock-section-DCMEFJa5.js";import{S as ae}from"./status-badge-EB_R6QG4.js";import"./pos-api-CYJtTNYA.js";import{u as re}from"./useMutation-DGZ6hM-V.js";import{u as oe}from"./stores-api-SKuCFAOT.js";import{C as ie}from"./index-BVMwYbkN.js";import{c as D}from"./createLucideIcon-BCCeA4NW.js";import{S as ce}from"./square-pen-BAI_ByPT.js";import{u as le,e as de,f as F}from"./index-DLl1pUb8.js";import{T as me,a as he,b as A,c as ue,d as xe,e as B}from"./table-yR0osUgs.js";import{I as pe}from"./input-42MGwRgA.js";import{F as ge}from"./filter-dropdown-DQ5-lk69.js";import{D as fe,a as Se,b as je,c as H}from"./dropdown-menu-DxrvXCFj.js";import{a as ye,b as Ce,u as G}from"./use-stores-FXDW9bYw.js";import{S as Ne}from"./skeleton-BP3IiuyP.js";import{P as W}from"./modal-BEyJXC3x.js";import{A as Te}from"./arrow-up-down-C8Aeggrv.js";import{I as ve}from"./IconPlus-CP5g8l93.js";import"./vietqr-api-CdtuA3dS.js";import{u as we,a as be}from"./use-images-DI1kfhNA.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";import{M as De}from"./multi-select-combobox-DmVdt6M6.js";import{U as V}from"./upload-Coojo4x4.js";import{I as _e}from"./image-DJbQr-DC.js";import{u as Me}from"./use-removed-items-Do_IGH04.js";import"./separator-DsrkJotP.js";import"./avatar-C2xen_AP.js";import"./search-context-GGIHanUV.js";import"./command-WbkkLvMj.js";import"./calendar-DaAHj6Vo.js";import"./index-BmCCDDB3.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DGVckEWq.js";import"./search-CX_1dbj5.js";import"./createReactComponent-CV7I4pf9.js";import"./scroll-area-DCgoUvjl.js";import"./index-BvxNbdt9.js";import"./select-LfFpGbbb.js";import"./index-D_lRkZQY.js";import"./check-B956xcJg.js";import"./IconChevronRight-BwCnOkVS.js";import"./IconSearch-K-wA4kRr.js";import"./chevron-right-CzKIkqDA.js";import"./react-icons.esm-tBZuCPpJ.js";import"./popover-BVjXu8gX.js";import"./TileLayer-DkwX82_G.js";import"./date-picker-B1HrUNhQ.js";import"./calendar-CzxbWO87.js";import"./combobox-Cw6fZwGg.js";import"./chevrons-up-down-BkQP1fiS.js";import"./badge-CSxw3ZSI.js";import"./utils-km2FGkQ4.js";import"./index-QWmA5vou.js";import"./useQuery-w3l-JMwb.js";import"./query-keys-xK68hCS0.js";import"./images-api-BaEYqJtb.js";import"./checkbox-C0erTYQj.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]],Ie=D("handshake",ke);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ee=D("house",Le);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],Oe=D("info",Ae);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]],Ke=D("monitor",Pe);function Re({expiryTimestamp:t}){if(!t)return e.jsx("span",{className:"text-muted-foreground",children:"-"});const n=new Date(t*1e3);return n<new Date?e.jsx("span",{className:"rounded bg-red-100 px-2 py-1 text-xs text-red-600",children:C.EXPIRED_LICENSE_BADGE}):e.jsx("span",{className:"font-medium",children:n.toLocaleDateString("vi-VN")})}function Ue({store:t}){const n=t.fb_store_id||t.code;return e.jsx("span",{className:"font-medium",children:n})}function Fe({store:t}){const n=t.active===U.ACTIVE||t.status===U.ACTIVE_TEXT;return e.jsx(ae,{isActive:n,activeText:C.BUTTON_ACTIVE,inactiveText:C.BUTTON_DEACTIVE})}const Be=({store:t})=>{var m;const[n,s]=p.useState(!1),{auth:a}=q(),o=((m=t.extraData)==null?void 0:m.is_franchise)===1,i=re({mutationFn:oe,onSuccess:()=>{T.success(`Đã cập nhật loại hình nhà hàng thành ${o?"thuộc chuỗi thương hiệu":"nhượng quyền"}`),s(!1),window.location.reload()},onError:d=>{T.error(d.message||"Có lỗi xảy ra khi cập nhật loại hình nhà hàng")}}),r=()=>{var x;const d=a.company,u=(x=a.brands)==null?void 0:x[0];if(!(d!=null&&d.id)||!(u!=null&&u.id)){T.error("Không tìm thấy thông tin công ty hoặc thương hiệu");return}i.mutate({company_uid:d.id,brand_uid:u.id,id:t.id,is_franchise:o?0:1})},l=o?{title:`Bạn xác nhận thay đổi ${t.name}`,content:"Từ điểm nhượng quyền thành điểm thuộc chuỗi thương hiệu",confirmText:"Xác nhận",cancelText:"Hủy"}:{title:`Bạn xác nhận thay đổi ${t.name}`,content:"Từ điểm thuộc chuỗi thương hiệu thành điểm nhượng quyền",confirmText:"Xác nhận",cancelText:"Hủy"};return e.jsxs("div",{onClick:d=>d.stopPropagation(),children:[e.jsxs(v,{variant:"ghost",size:"sm",className:`flex w-fit items-center justify-center gap-2 rounded px-2 py-1 text-white ${o?"bg-orange-500 hover:bg-orange-600":"bg-blue-500 hover:bg-blue-600"}`,onClick:()=>s(!0),disabled:i.isPending,children:[o?e.jsx(Ie,{className:"h-4 w-4"}):e.jsx(Ee,{className:"h-4 w-4"}),e.jsx(ce,{className:"h-4 w-4"})]}),e.jsx(ie,{open:n,onOpenChange:s,title:l.title,content:l.content,confirmText:l.confirmText,cancelText:l.cancelText,onConfirm:r,isLoading:i.isPending})]})},He=[{accessorKey:"id",header:"#",cell:({row:t})=>{const n=t.index+1;return e.jsx("div",{className:"w-8 text-xs font-medium sm:w-[50px] sm:text-sm",children:n})},enableSorting:!1},{accessorKey:"fb_store_id",header:"Pos ID",cell:({row:t})=>e.jsx(Ue,{store:t.original})},{accessorKey:"name",header:"Tên",cell:({row:t})=>e.jsx("span",{className:"font-medium",children:t.original.name})},{accessorKey:"city_name",header:"Địa điểm",cell:({row:t})=>{const n=t.original,s=n.city_name||n.cityName,a=n.address,o=n.latitude,i=n.longitude;return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:s}),a&&e.jsx(Q,{children:e.jsxs(Z,{children:[e.jsx(ee,{asChild:!0,children:e.jsx(Oe,{className:"text-muted-foreground hover:text-foreground h-4 w-4 cursor-help transition-colors"})}),e.jsx(te,{className:"max-w-xs",children:e.jsxs("div",{className:"space-y-1",children:[e.jsx("p",{className:"text-sm",children:a}),o&&i&&e.jsxs("p",{className:"text-xs",children:["lat: ",o,", long: ",i]})]})})]})})]})}},{accessorKey:"phone",header:"Số điện thoại",cell:({row:t})=>{const n=t.original;return n.phone?e.jsx("span",{className:"font-medium",children:n.phone}):e.jsx("span",{className:"text-muted-foreground",children:"-"})}},{accessorKey:"email",header:"Email",cell:({row:t})=>{const n=t.original;return n.email?e.jsx("span",{className:"font-medium",children:n.email}):e.jsx("span",{className:"text-muted-foreground",children:"-"})}},{accessorKey:"expiry_date",header:"Thời hạn bán quyền",cell:({row:t})=>{const n=t.original,s=n.expiry_date||n.expiryDate;return e.jsx(Re,{expiryTimestamp:s})}},{accessorKey:"store_type",header:"Loại hình nhà hàng",cell:({row:t})=>e.jsx(Be,{store:t.original})},{accessorKey:"active",header:"",cell:({row:t})=>e.jsx(Fe,{store:t.original})}];function Ve({columns:t,data:n}){var i;const s=z(),a=le({data:n,columns:t,getCoreRowModel:de()}),o=r=>{s({to:`/setting/store/detail/${r.id}`})};return e.jsx("div",{className:"w-full overflow-auto",children:e.jsx("div",{className:"min-w-full rounded-md border",children:e.jsxs(me,{className:"w-full table-auto",children:[e.jsx(he,{children:a.getHeaderGroups().map(r=>e.jsx(A,{className:"h-6 sm:h-7",children:r.headers.map(c=>e.jsx(ue,{className:"px-1 py-0.5 text-xs font-medium whitespace-nowrap sm:px-2 sm:py-1",children:c.isPlaceholder?null:F(c.column.columnDef.header,c.getContext())},c.id))},r.id))}),e.jsx(xe,{children:(i=a.getRowModel().rows)!=null&&i.length?a.getRowModel().rows.map(r=>e.jsx(A,{"data-state":r.getIsSelected()&&"selected",className:"hover:bg-muted/50 h-8 cursor-pointer sm:h-10",onClick:()=>o(r.original),children:r.getVisibleCells().map(c=>e.jsx(B,{className:"px-1 py-0.5 text-xs sm:px-2 sm:py-1 sm:text-sm",children:F(c.column.columnDef.cell,c.getContext())},c.id))},r.id)):e.jsx(A,{children:e.jsx(B,{colSpan:t.length,className:"h-12 text-center text-xs sm:h-16 sm:text-sm",children:"Không có dữ liệu cửa hàng."})})})]})})})}function $e({value:t,onValueChange:n,cities:s,isLoading:a,placeholder:o="Tất cả thành phố",className:i="w-48"}){const r=(s==null?void 0:s.map(c=>({value:c.id,label:c.name})))||[];return e.jsx(ge,{value:t,onValueChange:n,options:r,isLoading:a,placeholder:o,className:i,allOptionLabel:"Tất cả thành phố",loadingText:"Đang tải thành phố...",emptyText:"Không có thành phố"})}const qe=8,ze=6,$="text/plain",b={title:"Sắp xếp cửa hàng",confirmText:"Lưu",maxWidth:"sm:max-w-4xl",description:"Thứ tự hiển thị các cửa hàng sẽ được áp dụng tại hệ thống"},X={loadError:"Có lỗi xảy ra khi tải danh sách cửa hàng",noStores:"Không có cửa hàng nào để sắp xếp"},g={description:"text-muted-foreground text-sm",errorText:"text-sm text-red-600",emptyState:"py-8 text-center",grid:`grid grid-cols-${ze} gap-2`,draggableItem:"flex aspect-square cursor-move items-center justify-center bg-slate-300 p-1 transition-colors select-none hover:bg-slate-400 rounded-sm text-xs",itemText:"text-center text-xs font-medium leading-tight",skeletonContainer:"aspect-square bg-slate-300 p-1 rounded-sm"};function Ge(){return e.jsx("div",{className:g.grid,children:Array.from({length:qe}).map((t,n)=>e.jsx("div",{className:g.skeletonContainer,children:e.jsx(Ne,{className:"h-4 w-full"})},n))})}function We(){return e.jsx("div",{className:g.emptyState,children:e.jsx("p",{className:g.errorText,children:X.loadError})})}function Xe(){return e.jsx("div",{className:g.emptyState,children:e.jsx("p",{className:g.description,children:X.noStores})})}function Ye({store:t,index:n,onDragStart:s,onDragOver:a,onDrop:o}){return e.jsx("div",{draggable:!0,onDragStart:i=>s(i,n),onDragOver:a,onDrop:i=>o(i,n),className:g.draggableItem,children:e.jsx("div",{className:g.itemText,children:t.store_name})},t.id)}function Je(t){return p.useMemo(()=>{if(!(t!=null&&t.data))return[];const n=s=>s.active===1;return t.data.filter(n).sort((s,a)=>s.sort-a.sort)},[t==null?void 0:t.data])}function Qe(t){const[n,s]=p.useState([]);return p.useEffect(()=>{s(t)},[t]),{sortedStores:n,handleDragStart:(r,c)=>{r.dataTransfer.setData($,c.toString())},handleDragOver:r=>{r.preventDefault()},handleDrop:(r,c)=>{r.preventDefault();const l=parseInt(r.dataTransfer.getData($));if(l===c)return;const m=[...n],d=m[l];m.splice(l,1),m.splice(c,0,d),s(m)}}}function Ze({open:t,onOpenChange:n}){const{data:s,isLoading:a,error:o}=ye(),{updateSort:i,isUpdating:r}=Ce(),c=Je(s),{sortedStores:l,handleDragStart:m,handleDragOver:d,handleDrop:u}=Qe(c),x=a||r,f=l.length===0,N=()=>{const S=l.map((j,h)=>({...j,sort:h}));i(S,{onSuccess:()=>n(!1)})},w=()=>{n(!1)},_=()=>a?e.jsx(Ge,{}):o?e.jsx(We,{}):f?e.jsx(Xe,{}):e.jsx("div",{className:g.grid,children:l.map((S,j)=>e.jsx(Ye,{store:S,index:j,onDragStart:m,onDragOver:d,onDrop:u},S.id))});return e.jsxs(W,{open:t,onOpenChange:n,title:b.title,onCancel:w,onConfirm:N,confirmText:b.confirmText,hideButtons:!1,confirmDisabled:x,isLoading:r,disableCancelButton:!0,maxWidth:b.maxWidth,children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:g.description,children:b.description})}),e.jsx("div",{children:_()})]})}function et({onSyncSecondaryScreen:t}){const[n,s]=ne.useState(!1),a=()=>{s(!0)};return e.jsxs(e.Fragment,{children:[e.jsxs(fe,{children:[e.jsx(Se,{asChild:!0,children:e.jsx(v,{variant:"outline",size:"sm",children:"Tiện ích"})}),e.jsxs(je,{align:"end",children:[e.jsxs(H,{onClick:a,children:[e.jsx(Te,{className:"mr-2 h-4 w-4"}),"Sắp xếp cửa hàng"]}),e.jsxs(H,{onClick:t,children:[e.jsx(Ke,{className:"mr-2 h-4 w-4"}),"Đồng bộ màn hình phụ"]})]})]}),n&&e.jsx(Ze,{open:n,onOpenChange:s})]})}function O({searchTerm:t,onSearchChange:n,selectedCity:s,onCityChange:a,cities:o,citiesLoading:i,onCreateStore:r,onSyncSecondaryScreen:c}){return e.jsx("div",{className:"mb-4 space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex min-w-0 flex-1 items-center gap-3",children:[e.jsx("h2",{className:"text-xl font-semibold whitespace-nowrap",children:"Danh sách nhà hàng"}),e.jsx(pe,{placeholder:"Tìm kiếm nhà hàng",value:t,onChange:l=>n(l.target.value),className:"w-64"}),e.jsx($e,{value:s,onValueChange:a,cities:o,isLoading:i})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(et,{onSyncSecondaryScreen:c}),e.jsxs(v,{onClick:r,children:[e.jsx(ve,{className:"mr-2 h-4 w-4"}),"Tạo nhà hàng mới"]})]})]})})}function tt({open:t,onOpenChange:n}){const{data:s=[]}=G(),{company:a,brands:o}=q(h=>h.auth),i=o==null?void 0:o[0],[r,c]=p.useState([]),[l,m]=p.useState(null),[d,u]=p.useState(null),x=p.useRef(null),f=we(),N=be(),w=()=>{c([]),m(null),u(null),n(!1)},_=async()=>{if(r.length===0){T.error("Vui lòng chọn ít nhất một cửa hàng");return}if(!(a!=null&&a.id)||!(i!=null&&i.id)){T.error("Thiếu thông tin công ty hoặc thương hiệu");return}try{let h="";l&&(h=(await f.mutateAsync(l)).data.image_url),await N.mutateAsync({company_uid:a.id,brand_uid:i.id,list_store_uid:r,background:h}),c([]),m(null),u(null),n(!1)}catch(h){console.error("Sync failed:",h)}},S=h=>{var K;const y=(K=h.target.files)==null?void 0:K[0];if(!y)return;const Y=2.5*1024*1024;if(y.size>Y){alert("Kích thước ảnh phải nhỏ hơn 2.5MB");return}if(!y.type.startsWith("image/")){alert("Vui lòng chọn file ảnh");return}m(y);const P=new FileReader;P.onload=J=>{var R;u((R=J.target)==null?void 0:R.result)},P.readAsDataURL(y)},j=()=>{var h;(h=x.current)==null||h.click()};return e.jsx(W,{open:t,onOpenChange:n,title:"Đồng bộ màn hình phụ",onCancel:w,onConfirm:_,confirmText:"Đồng bộ",confirmDisabled:r.length===0||f.isPending||N.isPending,maxWidth:"sm:max-w-[600px] sm:min-h-[500px]",disableCancelButton:!0,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Chọn cửa hàng"}),e.jsx(De,{options:s.map(h=>({value:h.id,label:h.name})),value:r,onValueChange:c,placeholder:"Chọn cửa hàng để đồng bộ",searchPlaceholder:"Tìm kiếm cửa hàng...",emptyText:"Không tìm thấy cửa hàng.",selectAllLabel:"Chọn tất cả",allSelectedText:"Tất cả cửa hàng",selectedCountText:h=>`${h} cửa hàng đã chọn`,className:"w-full justify-between"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium",children:"Hình nền trên thiết bị bán hàng"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Hình ảnh này xuất hiện trên màn hình 2 của các thiết bị pos 2 màn hình. Kích thước đề xuất 1920x1080 px"}),e.jsx("input",{ref:x,type:"file",accept:"image/*",onChange:S,className:"hidden"}),e.jsxs("div",{className:"min-h-[200px] rounded-lg border-2 border-dashed border-gray-300 p-6",children:[d&&e.jsxs("div",{className:"space-y-3",children:[e.jsx("img",{src:d,alt:"Preview",className:"h-48 w-full rounded-lg object-cover"}),e.jsxs(v,{type:"button",variant:"outline",size:"sm",onClick:j,className:"w-full",children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Thay đổi ảnh"]})]}),!d&&e.jsxs("div",{className:"space-y-3 text-center",children:[e.jsx(_e,{className:"mx-auto h-12 w-12 text-gray-400"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Tải lên một hình ảnh hồ sơ"}),e.jsxs(v,{type:"button",variant:"outline",size:"sm",onClick:j,children:[e.jsx(V,{className:"mr-2 h-4 w-4"}),"Chọn ảnh"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Kích thước ảnh nhỏ hơn 2.5MB"})]})]})]})]})})}function nt(){const[t,n]=p.useState(""),[s,a]=p.useState(C.CITY_ALL);return{searchTerm:t,setSearchTerm:n,selectedCity:s,setSelectedCity:a}}function st(t=[]){return{cities:t.map(s=>({id:s.city_id||s.id,name:s.city_name}))}}function at({stores:t,searchTerm:n,selectedCity:s}){return{filteredStores:t.filter(o=>{const i=o.name.toLowerCase().includes(n.toLowerCase())||o.code.toLowerCase().includes(n.toLowerCase())||o.address.toLowerCase().includes(n.toLowerCase())||o.phone.toLowerCase().includes(n.toLowerCase())||o.email.toLowerCase().includes(n.toLowerCase()),r=o.cityId;return i&&(s==="all"||r===s)})}}function rt(){const t=z(),[n,s]=p.useState(!1),{searchTerm:a,setSearchTerm:o,selectedCity:i,setSelectedCity:r}=nt(),{data:c=[],isLoading:l,error:m}=G(),{data:d=[],isLoading:u}=Me(),{cities:x}=st(d),{filteredStores:f}=at({stores:c,searchTerm:a,selectedCity:i});return{stores:c,filteredStores:f,cities:x,isLoading:l,citiesLoading:u,error:m,searchTerm:a,setSearchTerm:o,selectedCity:i,setSelectedCity:r,handleCreateStore:()=>{t({to:C.ROUTE_STORE_DETAIL})},handleSyncSecondaryScreen:()=>{s(!0)},syncModalOpen:n,setSyncModalOpen:s}}function ot(){const{filteredStores:t,cities:n,isLoading:s,citiesLoading:a,error:o,searchTerm:i,setSearchTerm:r,selectedCity:c,setSelectedCity:l,handleCreateStore:m,handleSyncSecondaryScreen:d,syncModalOpen:u,setSyncModalOpen:x}=rt();return o?e.jsxs(e.Fragment,{children:[e.jsx(M,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(L,{}),e.jsx(E,{}),e.jsx(I,{})]})}),e.jsx(k,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(O,{searchTerm:i,onSearchChange:r,selectedCity:c,onCityChange:l,cities:n,citiesLoading:a,onCreateStore:m,onSyncSecondaryScreen:d}),e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:se(o)})})]})})]}):s?e.jsxs(e.Fragment,{children:[e.jsx(M,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(L,{}),e.jsx(E,{}),e.jsx(I,{})]})}),e.jsx(k,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(O,{searchTerm:i,onSearchChange:r,selectedCity:c,onCityChange:l,cities:n,citiesLoading:a,onCreateStore:m,onSyncSecondaryScreen:d}),e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:C.LOADING_STORES})})]})})]}):e.jsxs(e.Fragment,{children:[e.jsx(M,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(L,{}),e.jsx(E,{}),e.jsx(I,{})]})}),e.jsx(k,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(O,{searchTerm:i,onSearchChange:r,selectedCity:c,onCityChange:l,cities:n,citiesLoading:a,onCreateStore:m,onSyncSecondaryScreen:d}),e.jsx(Ve,{columns:He,data:t}),e.jsx(tt,{open:u,onOpenChange:x})]})})]})}const yn=function(){return e.jsx(ot,{})};export{yn as component};
