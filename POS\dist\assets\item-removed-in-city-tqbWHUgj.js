import{j as e,f as V,B as v,r as p,a4 as j}from"./index-DLSysrh4.js";import{g as R}from"./error-utils-BtHHKnij.js";import"./pos-api-CYJtTNYA.js";import"./vietqr-api-CdtuA3dS.js";import{a as H,b as L,u as $,e as A}from"./use-removed-items-Do_IGH04.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";import{H as O}from"./header-BO8CjX_w.js";import{M as z}from"./main-W6TPZi-0.js";import{C as U}from"./index-BVMwYbkN.js";import"./date-range-picker-CoReVTN3.js";import"./form-DCttU9FS.js";import{P as G}from"./profile-dropdown-D5DnkWo7.js";import{S as Q,T as W}from"./search-CCOrl5KY.js";import{C as _}from"./checkbox-C0erTYQj.js";import{I as X}from"./IconRestore-CO243XRz.js";import{f as J}from"./isSameMonth-C8JQo-AN.js";import{u as q,e as Y,f as k}from"./index-DLl1pUb8.js";import{T as Z,a as ee,b as C,c as te,d as se,e as M}from"./table-yR0osUgs.js";import{I as oe}from"./input-42MGwRgA.js";import{S as ne,a as re,b as ie,c as ae,d as T}from"./select-LfFpGbbb.js";import{E as le}from"./exceljs.min-Bt6AOS27.js";import{I as ce}from"./IconDownload-BoJDTHr5.js";import"./useQuery-w3l-JMwb.js";import"./utils-km2FGkQ4.js";import"./useMutation-DGZ6hM-V.js";import"./query-keys-xK68hCS0.js";import"./separator-DsrkJotP.js";import"./dialog-DGVckEWq.js";import"./calendar-DaAHj6Vo.js";import"./createLucideIcon-BCCeA4NW.js";import"./index-BmCCDDB3.js";import"./chevron-right-CzKIkqDA.js";import"./react-icons.esm-tBZuCPpJ.js";import"./popover-BVjXu8gX.js";import"./avatar-C2xen_AP.js";import"./dropdown-menu-DxrvXCFj.js";import"./index-BvxNbdt9.js";import"./index-QWmA5vou.js";import"./check-B956xcJg.js";import"./search-context-GGIHanUV.js";import"./command-WbkkLvMj.js";import"./search-CX_1dbj5.js";import"./createReactComponent-CV7I4pf9.js";import"./scroll-area-DCgoUvjl.js";import"./IconChevronRight-BwCnOkVS.js";import"./IconSearch-K-wA4kRr.js";import"./index-D_lRkZQY.js";const me=[{id:"select",header:({table:t})=>e.jsx(_,{checked:t.getIsAllPageRowsSelected()||t.getIsSomePageRowsSelected()&&"indeterminate",onCheckedChange:o=>t.toggleAllPageRowsSelected(!!o),"aria-label":"Select all"}),cell:({row:t})=>e.jsx(_,{checked:t.getIsSelected(),onCheckedChange:o=>t.toggleSelected(!!o),"aria-label":"Select row"}),enableSorting:!1,enableHiding:!1},{accessorKey:"id",header:"#",cell:({row:t})=>{const o=t.index+1;return e.jsx("div",{className:"w-[50px] font-medium",children:o})},enableSorting:!1},{accessorKey:"item_id",header:"Mã món",cell:({row:t})=>{const o=t.original;return e.jsx("span",{className:"font-medium",children:o.item_id})}},{accessorKey:"item_name",header:"Tên món",cell:({row:t})=>{const o=t.original;return e.jsx("span",{className:"font-medium",children:o.item_name})}},{accessorKey:"ta_price",header:"Giá",cell:({row:t})=>{const o=t.original;return e.jsx("span",{className:"font-medium",children:V(o.ta_price)})}},{accessorKey:"city_name",header:"Thành phố",cell:({row:t,table:o})=>{var d;const c=t.original,n=o.options.meta,a=((d=n==null?void 0:n.getCityName)==null?void 0:d.call(n,c.city_uid))||c.city_uid;return e.jsx("span",{children:a})}},{accessorKey:"deleted_by",header:"Người xoá",cell:({row:t})=>{const o=t.original;return e.jsx("span",{children:o.deleted_by})}},{accessorKey:"deleted_at",header:"Thời gian xoá",cell:({row:t})=>{const o=t.original;return e.jsx("span",{children:J(new Date(o.deleted_at*1e3),"dd/MM/yyyy HH:mm")})}},{id:"restore",header:"",cell:({row:t,table:o})=>{const c=t.original,n=o.options.meta;return e.jsxs(v,{variant:"ghost",size:"sm",onClick:()=>{var a;return(a=n==null?void 0:n.onRestoreItem)==null?void 0:a.call(n,c)},className:"h-8 w-8 p-0 text-blue-600 hover:text-blue-700",title:"Khôi phục món",children:[e.jsx(X,{className:"h-4 w-4"}),e.jsxs("span",{className:"sr-only",children:["Khôi phục món ",c.item_name]})]})}}];function de({columns:t,data:o,onRestoreItem:c,getCityName:n,onBulkRestore:a,clearSelection:d}){var l;const[x,m]=p.useState({});p.useEffect(()=>{d&&m({})},[d]);const i=q({data:o,columns:t,getCoreRowModel:Y(),enableRowSelection:!0,onRowSelectionChange:m,state:{rowSelection:x},meta:{onRestoreItem:c,getCityName:n}});return e.jsxs("div",{children:[i.getFilteredSelectedRowModel().rows.length>0&&a&&e.jsx("div",{className:"mb-4",children:e.jsx(v,{variant:"default",onClick:()=>{const s=i.getFilteredSelectedRowModel().rows.map(r=>r.original);a(s)},children:"Khôi phục"})}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(Z,{children:[e.jsx(ee,{children:i.getHeaderGroups().map(s=>e.jsx(C,{children:s.headers.map(r=>e.jsx(te,{children:r.isPlaceholder?null:k(r.column.columnDef.header,r.getContext())},r.id))},s.id))}),e.jsx(se,{children:(l=i.getRowModel().rows)!=null&&l.length?i.getRowModel().rows.map(s=>e.jsx(C,{"data-state":s.getIsSelected()&&"selected",children:s.getVisibleCells().map(r=>e.jsx(M,{children:k(r.column.columnDef.cell,r.getContext())},r.id))},s.id)):e.jsx(C,{children:e.jsx(M,{colSpan:t.length,className:"h-24 text-center",children:"Không có dữ liệu món đã xóa."})})})]})}),e.jsx("div",{className:"flex items-center justify-end space-x-2 py-4",children:e.jsxs("div",{className:"text-muted-foreground text-sm",children:[i.getFilteredSelectedRowModel().rows.length," of"," ",i.getFilteredRowModel().rows.length," row(s) selected."]})})]})}function he(t){return new Intl.NumberFormat("vi-VN",{style:"currency",currency:"VND"}).format(t)}function ue(t){const o=new Date(t*1e3),c=o.toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"}),n=o.toLocaleTimeString("vi-VN",{hour:"2-digit",minute:"2-digit",hour12:!1});return`${c} ${n}`}async function pe(t,o,c="removed-items-report.xlsx"){const n=new le.Workbook,a=t.reduce((l,s)=>(l[s.city_uid]||(l[s.city_uid]=[]),l[s.city_uid].push(s),l),{});Object.entries(a).forEach(([l,s])=>{const r=o.find(h=>h.id===l),w=(r==null?void 0:r.city_name)||`City ${l}`,g=n.addWorksheet(w),S=g.addRow([`Món đã xoá tại thành phố ${w}`]);S.font={bold:!0,size:14},S.alignment={horizontal:"center"},g.mergeCells("A1:E1");const f=g.addRow(["Mã món","Tên món","Giá","Người xoá","Thời gian xoá"]);f.font={bold:!0,color:{argb:"FFFFFF"}},f.fill={type:"pattern",pattern:"solid",fgColor:{argb:"4472C4"}},f.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}},s.forEach(h=>{g.addRow([h.item_id,h.item_name,he(h.ots_price),h.deleted_by,ue(h.deleted_at)])}),g.columns=[{width:15},{width:30},{width:15},{width:25},{width:20}]});const d=await n.xlsx.writeBuffer(),x=new Blob([d],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),m=window.URL.createObjectURL(x),i=document.createElement("a");i.href=m,i.download=c,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(m)}function xe({searchQuery:t,onSearchQueryChange:o,onSearchSubmit:c,selectedCityId:n,onCityChange:a,cities:d,removedItems:x}){const[m,i]=p.useState(!1),l=async()=>{try{i(!0);const s=n==="all"?x||[]:(x||[]).filter(r=>r.city_uid===n);if(s.length===0){j.error("Không có dữ liệu để xuất báo cáo");return}await pe(s,d),j.success("Báo cáo đã được xuất thành công!")}catch(s){const r=R(s);j.error(r)}finally{i(!1)}};return e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Món đã xoá"}),e.jsx(oe,{placeholder:"Tìm kiếm món đã xóa...",className:"w-64",value:t,onChange:s=>o(s.target.value),onKeyDown:s=>{s.key==="Enter"&&(s.preventDefault(),c(t))}}),e.jsxs(ne,{value:n,onValueChange:a,children:[e.jsx(re,{className:"w-48",children:e.jsx(ie,{placeholder:"Chọn thành phố"})}),e.jsxs(ae,{children:[e.jsx(T,{value:"all",children:"Tất cả các thành phố"}),d.map(s=>e.jsx(T,{value:s.id,children:s.city_name},s.id))]})]})]}),e.jsxs(v,{size:"sm",onClick:l,disabled:m,children:[e.jsx(ce,{className:"mr-2 h-4 w-4"}),m?"Đang xuất...":"Xuất báo cáo"]})]})}function ge(){const[t,o]=p.useState(""),[c,n]=p.useState(""),[a,d]=p.useState("all"),[x,m]=p.useState(!1),[i,l]=p.useState(null),[s,r]=p.useState([]),[w,g]=p.useState(!1),S=H(),f=L(),{data:h=[]}=$(),D=p.useMemo(()=>a==="all"||!a?h.map(u=>u.id):[a],[a,h]),{data:N,isLoading:I,error:b}=A({searchTerm:t||void 0,listCityUid:D}),E=u=>{l(u),r([]),m(!0)},B=async()=>{try{if(s.length>0){const u=s.map(y=>y.id);await f.mutateAsync(u),j.success(`${s.length} món đã được khôi phục thành công!`),r([]),g(!0),setTimeout(()=>g(!1),100)}else i&&(await f.mutateAsync([i.id]),j.success(`Món "${i.item_name}" đã được khôi phục thành công!`),l(null));m(!1)}catch(u){const y=R(u);j.error(y)}},K=u=>{const y=h.find(P=>P.id===u);return(y==null?void 0:y.city_name)||u},F=u=>{r(u),l(null),m(!0)};return e.jsxs(e.Fragment,{children:[e.jsx(O,{children:e.jsxs("div",{className:"ml-auto flex items-center space-x-4",children:[e.jsx(Q,{}),e.jsx(W,{}),e.jsx(G,{})]})}),e.jsx(z,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(xe,{searchQuery:c,onSearchQueryChange:n,onSearchSubmit:o,selectedCityId:a,onCityChange:d,cities:h,removedItems:N||[]}),b&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{className:"text-red-600",children:R(b)})}),!b&&I&&e.jsx("div",{className:"py-8 text-center",children:e.jsx("p",{children:"Đang tải dữ liệu món đã xóa..."})}),!b&&!I&&e.jsx(de,{columns:me,data:N||[],onRestoreItem:E,getCityName:K,onBulkRestore:F,clearSelection:w}),e.jsx(U,{open:x,onOpenChange:m,content:s.length>0?`Bạn có muốn khôi phục ${s.length} món đã chọn?`:"Bạn có muốn khôi phục?",confirmText:"Xác nhận",onConfirm:B,isLoading:S.isPending||f.isPending})]})})]})}const dt=ge;export{dt as component};
