import{e as a,r as p,j as t}from"./index-DLSysrh4.js";import{I as e}from"./item-detail-form-TuM1pMDu.js";import"./form-DCttU9FS.js";import"./pos-api-CYJtTNYA.js";import{g as c}from"./customization-dialog-C_p9-QXm.js";import"./user-QGmeemH2.js";import"./vietqr-api-CdtuA3dS.js";import"./crm-api-B_UJzTcg.js";import"./header-BO8CjX_w.js";import"./main-W6TPZi-0.js";import"./search-context-GGIHanUV.js";import"./date-range-picker-CoReVTN3.js";import"./multi-select-DB8GW7bS.js";import"./zod-j2m7p1zB.js";import"./use-upload-image-DTozGwxx.js";import"./images-api-BaEYqJtb.js";import"./use-item-types-CSsfT6lm.js";import"./useQuery-w3l-JMwb.js";import"./utils-km2FGkQ4.js";import"./useMutation-DGZ6hM-V.js";import"./query-keys-xK68hCS0.js";import"./use-item-classes-DGMAg27S.js";import"./use-units-gLxkZ0xs.js";import"./use-items-BP05OniH.js";import"./item-api-DRVMGKoy.js";import"./use-removed-items-Do_IGH04.js";import"./use-customizations-CPiPqWYz.js";import"./use-customization-by-id-BZnh-lz-.js";import"./use-sources-CYxjcLqt.js";import"./sources-api-BViufi-a.js";import"./sources-CfiQ7039.js";import"./calendar-DaAHj6Vo.js";import"./createLucideIcon-BCCeA4NW.js";import"./index-BmCCDDB3.js";import"./isSameMonth-C8JQo-AN.js";import"./checkbox-C0erTYQj.js";import"./index-D_lRkZQY.js";import"./check-B956xcJg.js";import"./input-42MGwRgA.js";import"./textarea-DA1C5L_X.js";import"./combobox-Cw6fZwGg.js";import"./command-WbkkLvMj.js";import"./dialog-DGVckEWq.js";import"./search-CX_1dbj5.js";import"./popover-BVjXu8gX.js";import"./chevrons-up-down-BkQP1fiS.js";import"./upload-Coojo4x4.js";import"./collapsible-u38SogMV.js";import"./confirm-dialog-DflbPhcO.js";import"./alert-dialog-jC5TDKEJ.js";import"./date-picker-B1HrUNhQ.js";import"./calendar-CzxbWO87.js";import"./circle-help-Bn10grtQ.js";import"./select-LfFpGbbb.js";import"./index-BvxNbdt9.js";import"./chevron-right-CzKIkqDA.js";import"./use-dialog-state-BCMBa9nj.js";import"./modal-BEyJXC3x.js";import"./xlsx-DkH2s96g.js";import"./separator-DsrkJotP.js";import"./createReactComponent-CV7I4pf9.js";import"./scroll-area-DCgoUvjl.js";import"./IconChevronRight-BwCnOkVS.js";import"./react-icons.esm-tBZuCPpJ.js";import"./badge-CSxw3ZSI.js";import"./circle-x-DosBFIxj.js";const vt=function(){const i=a({from:"/_authenticated/menu/items/items-in-city/detail"}),[r,s]=p.useState(null),o=i==null?void 0:i.id,{data:m,isLoading:n}=c(o,!!o);return o?(p.useEffect(()=>{m&&s(m.data)},[m]),n||!r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"flex items-center justify-center",children:t.jsx("div",{className:"text-lg",children:"Đang tải..."})})}):t.jsx(e,{currentRow:{...r,item_id:"",item_id_barcode:""},isCopyMode:!!r})):t.jsx(e,{})};export{vt as component};
