import{j as e,r as d,R as U,B as _,h as P,a4 as f}from"./index-DLSysrh4.js";import{P as E}from"./modal-BEyJXC3x.js";import"./pos-api-CYJtTNYA.js";import"./date-range-picker-CoReVTN3.js";import{L as V}from"./form-DCttU9FS.js";import{I as z}from"./input-42MGwRgA.js";import{T as B,a as H,b as O,c as q,d as $,e as D}from"./table-yR0osUgs.js";import{C as T}from"./checkbox-C0erTYQj.js";import{C as k,a as R,b as A}from"./collapsible-u38SogMV.js";import{C as G}from"./select-LfFpGbbb.js";import{C as L}from"./chevron-right-CzKIkqDA.js";import{D as W,a as K,e as Q}from"./dialog-DGVckEWq.js";import"./vietqr-api-CdtuA3dS.js";import{a as Y}from"./use-customizations-CPiPqWYz.js";import{u as X}from"./use-update-customization-_k_-0Lgv.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";function pe({open:c,onOpenChange:n,onCancel:a,onConfirm:m,onAddMenuItem:u,isEditing:h,customizationName:g,groupName:x,setGroupName:j,minRequired:C,setMinRequired:N,maxAllowed:l,setMaxAllowed:v,menuItems:S}){return e.jsx(E,{title:`${h?"Sửa":"Tạo"} nhóm cho customization ${g||""}`,open:c,onOpenChange:n,onCancel:a,onConfirm:m,confirmText:"Lưu",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx(z,{placeholder:"Tên nhóm",value:x,onChange:t=>j(t.target.value)})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(V,{htmlFor:"min-required",className:"min-w-[80px] text-sm font-medium",children:"Yêu cầu"}),e.jsx(z,{id:"min-required",type:"number",value:C,onChange:t=>N(t.target.value),min:"0",className:"flex-1"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(V,{htmlFor:"max-allowed",className:"min-w-[80px] text-sm font-medium",children:"Tối đa"}),e.jsx(z,{id:"max-allowed",type:"number",value:l,onChange:t=>v(t.target.value),min:"0",className:"flex-1"})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium",children:"Danh sách món"}),e.jsx("div",{className:"rounded-md border",children:e.jsxs(B,{children:[e.jsx(H,{children:e.jsxs(O,{children:[e.jsx(q,{children:"Tên"}),e.jsx(q,{children:"Giá"})]})}),e.jsxs($,{children:[S.map(t=>e.jsxs(O,{children:[e.jsx(D,{children:t.name}),e.jsxs(D,{children:[t.price.toLocaleString("vi-VN")," đ"]})]},t.id)),e.jsx(O,{className:"cursor-pointer hover:bg-gray-50",onClick:u,children:e.jsx(D,{colSpan:2,className:"py-4 text-center",children:e.jsx("span",{className:"font-medium text-blue-600",children:"Thêm món"})})})]})]})})]})]})})}function fe({open:c,onOpenChange:n,onCancel:a,onConfirm:m,menuItemSearchTerm:u,setMenuItemSearchTerm:h,selectedMenuSectionOpen:g,setSelectedMenuSectionOpen:x,remainingMenuSectionOpen:j,setRemainingMenuSectionOpen:C,selectedMenuItems:N,handleMenuItemToggle:l,selectedMenuItemsList:v,remainingMenuItemsList:S}){return e.jsx(E,{title:"Chọn món để thêm vào nhóm",open:c,onOpenChange:n,onCancel:a,onConfirm:m,confirmText:"Xác nhận",cancelText:"Hủy",maxWidth:"sm:max-w-2xl",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(z,{placeholder:"Tìm kiếm món",value:u,onChange:t=>h(t.target.value),className:"w-full"}),e.jsxs(k,{open:g,onOpenChange:x,children:[e.jsxs(R,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Đã chọn (",v.length,")"]}),g?e.jsx(G,{className:"h-4 w-4"}):e.jsx(L,{className:"h-4 w-4"})]}),e.jsx(A,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:v.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Chưa có món nào được chọn"}):v.map(t=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(T,{checked:N.has(t.id),onCheckedChange:()=>l(t.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:t.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t.ots_price.toLocaleString("vi-VN")," đ"]})]})]},t.id))})})]}),e.jsxs(k,{open:j,onOpenChange:C,children:[e.jsxs(R,{className:"flex w-full items-center justify-between rounded-md border p-3 hover:bg-gray-50",children:[e.jsxs("span",{className:"font-medium",children:["Còn lại (",S.length,")"]}),j?e.jsx(G,{className:"h-4 w-4"}):e.jsx(L,{className:"h-4 w-4"})]}),e.jsx(A,{className:"mt-2",children:e.jsx("div",{className:"max-h-60 space-y-2 overflow-y-auto rounded-md border p-3",children:S.length===0?e.jsx("p",{className:"text-sm text-gray-500",children:"Không có món nào"}):S.map(t=>e.jsxs("label",{className:"flex cursor-pointer items-center space-x-3 rounded p-2 hover:bg-gray-50",children:[e.jsx(T,{checked:N.has(t.id),onCheckedChange:()=>l(t.id)}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-sm font-medium",children:t.item_name}),e.jsxs("p",{className:"text-xs text-gray-500",children:[t.ots_price.toLocaleString("vi-VN")," đ"]})]})]},t.id))})})]})]})})}function ge({open:c,onOpenChange:n,items:a,selectedItems:m,onItemsSelected:u}){const[h,g]=d.useState(""),[x,j]=d.useState(!1),[C,N]=d.useState(!1),[l,v]=d.useState(m);U.useEffect(()=>{v(m)},[m]);const S=d.useMemo(()=>{if(!h.trim())return a;const s=h.toLowerCase();return a.filter(p=>p.item_name.toLowerCase().includes(s)||p.item_id.toLowerCase().includes(s))},[a,h]),t=S.filter(s=>l.includes(s.item_id)),M=S.filter(s=>!l.includes(s.item_id)),o=s=>{const p=l.includes(s)?l.filter(y=>y!==s):[...l,s];v(p)},r=()=>{u(l),n(!1)},i=()=>{v(m),n(!1)};return e.jsx(W,{open:c,onOpenChange:n,children:e.jsxs(K,{className:"max-w-2xl",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold",children:"Chọn món áp dụng"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Chọn các món mà customization này sẽ áp dụng"})]}),e.jsx("div",{children:e.jsx(z,{placeholder:"Tìm kiếm món...",value:h,onChange:s=>g(s.target.value),className:"w-full"})}),e.jsxs(k,{open:!x,onOpenChange:j,children:[e.jsx(R,{asChild:!0,children:e.jsxs(_,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Đã chọn (",t.length,")"]}),x?e.jsx(L,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})}),e.jsx(A,{className:"space-y-2",children:t.length===0?e.jsx("p",{className:"p-2 text-sm text-gray-500",children:"Chưa có món nào được chọn"}):t.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!0,onCheckedChange:()=>o(s.item_id)}),e.jsx("span",{className:"text-sm",children:s.item_name})]},s.id))})]}),e.jsxs(k,{open:!C,onOpenChange:N,children:[e.jsx(R,{asChild:!0,children:e.jsxs(_,{variant:"ghost",className:"flex w-full items-center justify-between p-2 text-left",children:[e.jsxs("span",{children:["Còn lại (",M.length,")"]}),C?e.jsx(L,{className:"h-4 w-4"}):e.jsx(G,{className:"h-4 w-4"})]})}),e.jsx(A,{className:"space-y-2",children:M.length===0?e.jsx("p",{className:"p-2 text-sm text-gray-500",children:"Không có món nào"}):M.map(s=>e.jsxs("div",{className:"flex items-center space-x-2 p-2",children:[e.jsx(T,{checked:!1,onCheckedChange:()=>o(s.item_id)}),e.jsx("span",{className:"text-sm",children:s.item_name})]},s.id))})]})]}),e.jsxs(Q,{children:[e.jsx(_,{variant:"outline",onClick:i,children:"Hủy"}),e.jsx(_,{onClick:r,children:"Lưu"})]})]})})}function je(c={}){const n=P(),[a,m]=d.useState(""),[u,h]=d.useState(""),[g,x]=d.useState(!1),[j,C]=d.useState(null),N=Y(),l=X();return{customizationName:a,selectedStoreId:u,isSubmitting:g,setCustomizationName:m,setSelectedStoreId:h,setExistingCustomization:C,handleBack:()=>{n({to:"/menu/customization/customization-in-store"})},handleSave:async(t,M,o)=>{var r;if(!a.trim()){f.error("Vui lòng nhập tên customization");return}if(!u){f.error("Vui lòng chọn cửa hàng");return}if(t.length===0){f.error("Vui lòng tạo ít nhất một nhóm");return}if(M.size===0){f.error("Vui lòng chọn ít nhất một món áp dụng");return}x(!0);try{const i=t.map(w=>({LstItem_Id:w.items.map(b=>b.code||b.id),Min_Permitted:w.minRequired,Max_Permitted:w.maxAllowed,Name:w.name,id:w.id.startsWith("CUS_GROUP_")?w.id:`CUS_GROUP_${Math.random().toString(36).substring(2,7).toUpperCase()}`})),s=t.flatMap(w=>w.items.map(b=>b.code||b.id)),p=Array.from(M).map(w=>{const b=o.find(F=>F.id===w);return(b==null?void 0:b.item_id)||w}),y=[...new Set([...s,...p])],I={name:a.trim(),storeUid:u,data:{LstItem_Options:i},listItem:y,sort:1e3,isUpdateSameCustomization:!1};c.isEdit&&c.customizationId?(await l.mutateAsync({customizationId:c.customizationId,existingCustomization:j||void 0,...I}),f.success("Đã cập nhật customization thành công!")):(await N.mutateAsync(I),f.success("Đã tạo customization thành công!")),(r=c.onSuccess)==null||r.call(c),n({to:"/menu/customization/customization-in-store"})}catch{const i=c.isEdit?"cập nhật":"tạo";f.error(`Lỗi khi ${i} customization. Vui lòng thử lại.`)}finally{x(!1)}},isFormValid:a.trim()&&u}}function Ce(){const[c,n]=d.useState([]),[a,m]=d.useState(null),[u,h]=d.useState(""),[g,x]=d.useState("0"),[j,C]=d.useState("0"),[N,l]=d.useState([]),v=()=>{m(null),o()},S=r=>{const i=c.find(s=>s.id===r);i&&(m(r),h(i.name),x(i.minRequired.toString()),C(i.maxAllowed.toString()),l(i.items))},t=r=>{n(i=>i.filter(s=>s.id!==r)),f.success("Đã xóa nhóm thành công!")},M=r=>{if(!u.trim())return f.error("Vui lòng nhập tên nhóm"),!1;const i=parseInt(g),s=parseInt(j);if(isNaN(i)||i<0)return f.error("Yêu cầu phải là số hợp lệ"),!1;if(isNaN(s)||s<0)return f.error("Tối đa phải là số hợp lệ"),!1;if(N.length===0)return f.error("Vui lòng thêm ít nhất một món"),!1;const p={id:a||Date.now().toString(),name:u.trim(),minRequired:i,maxAllowed:s,items:N.map(y=>{const I=r.find(w=>w.id===y.id);return{...y,code:(I==null?void 0:I.item_id)||y.code||y.id,size:"M"}})};return a?(n(y=>y.map(I=>I.id===a?p:I)),f.success("Đã cập nhật nhóm thành công!")):(n(y=>[...y,p]),f.success("Đã tạo nhóm thành công!")),o(),!0},o=()=>{h(""),x("0"),C("0"),l([]),m(null)};return{customizationGroups:c,editingGroupId:a,groupName:u,minRequired:g,maxAllowed:j,menuItems:N,setGroupName:h,setMinRequired:x,setMaxAllowed:C,setMenuItems:l,setCustomizationGroups:n,handleCreateGroup:v,handleEditGroup:S,handleDeleteGroup:t,handleSaveGroup:M,resetGroupForm:o,isEditing:!!a}}function Ne(c={}){const[n,a]=d.useState(new Set),[m,u]=d.useState(""),[h,g]=d.useState(!0),[x,j]=d.useState(!0),C=o=>{const r=new Set(n);r.has(o)?r.delete(o):r.add(o),a(r)},N=o=>{var s;const i=o.filter(p=>n.has(p.id)).map(p=>({id:p.id,name:p.item_name,price:p.ots_price,code:p.item_id}));return(s=c.onConfirm)==null||s.call(c,i),l(),f.success("Cập nhật món thành công"),i},l=()=>{a(new Set),u(""),g(!0),j(!0)},v=o=>{const r=new Set(o.map(i=>i.id));a(r)},S=o=>o.filter(r=>r.item_name.toLowerCase().includes(m.toLowerCase()));return{selectedMenuItems:n,menuItemSearchTerm:m,selectedMenuSectionOpen:h,remainingMenuSectionOpen:x,setMenuItemSearchTerm:u,setSelectedMenuSectionOpen:g,setRemainingMenuSectionOpen:j,handleMenuItemToggle:C,handleConfirmMenuItems:N,resetSelection:l,setSelectedMenuItemsFromGroup:v,getFilteredMenuItems:S,getSelectedMenuItemsList:o=>S(o).filter(s=>n.has(s.id)),getRemainingMenuItemsList:o=>S(o).filter(s=>!n.has(s.id)),hasSelectedItems:n.size>0}}function Se(){const[c,n]=d.useState(!1),[a,m]=d.useState(!1),[u,h]=d.useState(!1);return{createGroupModalOpen:c,addItemModalOpen:a,dishModalOpen:u,setCreateGroupModalOpen:n,setAddItemModalOpen:m,setDishModalOpen:h,handleCloseModal:()=>{n(!1)},handleCloseAddItemModal:()=>{m(!1)},handleCloseDishModal:()=>{h(!1)},handleAddMenuItem:l=>l?(m(!0),!0):(f.error("Vui lòng chọn cửa hàng trước"),!1),handleOpenDishModal:l=>l?!1:(h(!0),!0)}}export{fe as A,pe as C,ge as I,Ce as a,Ne as b,Se as c,je as u};
