import{aT as t,j as m}from"./index-DLSysrh4.js";import"./pos-api-CYJtTNYA.js";import"./vietqr-api-CdtuA3dS.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";import"./header-BO8CjX_w.js";import"./main-W6TPZi-0.js";import"./search-context-GGIHanUV.js";import"./date-range-picker-CoReVTN3.js";import"./form-DCttU9FS.js";import{C as i}from"./create-table-form-3I7epZ2h.js";import"./separator-DsrkJotP.js";import"./command-WbkkLvMj.js";import"./calendar-DaAHj6Vo.js";import"./createLucideIcon-BCCeA4NW.js";import"./index-BmCCDDB3.js";import"./isSameMonth-C8JQo-AN.js";import"./dialog-DGVckEWq.js";import"./search-CX_1dbj5.js";import"./createReactComponent-CV7I4pf9.js";import"./scroll-area-DCgoUvjl.js";import"./index-BvxNbdt9.js";import"./select-LfFpGbbb.js";import"./index-D_lRkZQY.js";import"./check-B956xcJg.js";import"./IconChevronRight-BwCnOkVS.js";import"./chevron-right-CzKIkqDA.js";import"./react-icons.esm-tBZuCPpJ.js";import"./popover-BVjXu8gX.js";import"./use-areas-CqwB-Xb-.js";import"./useQuery-w3l-JMwb.js";import"./utils-km2FGkQ4.js";import"./useMutation-DGZ6hM-V.js";import"./images-api-BaEYqJtb.js";import"./query-keys-xK68hCS0.js";import"./use-sales-channels-1G3Jr-Zh.js";import"./use-tables-DLC5P1nX.js";import"./input-42MGwRgA.js";import"./checkbox-C0erTYQj.js";import"./collapsible-u38SogMV.js";import"./use-items-in-store-data-DrWSHVYc.js";import"./use-item-types-CSsfT6lm.js";import"./use-item-classes-DGMAg27S.js";import"./use-units-gLxkZ0xs.js";import"./use-removed-items-Do_IGH04.js";import"./items-in-store-api-BMUgbIkA.js";import"./xlsx-DkH2s96g.js";import"./copy-4Q0vH92V.js";import"./plus-4pOKdB3b.js";import"./minus-yNUVDxL8.js";const rt=function(){const{tableId:o}=t.useParams(),{store_uid:r}=t.useSearch();return m.jsx(i,{areaId:o,storeUid:r,fromTableLayout:!0})};export{rt as component};
