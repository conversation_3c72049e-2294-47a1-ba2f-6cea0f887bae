import{a4 as P,u as ue,r as y,h as pe,a3 as Se,j as e,B as T,T as $,o as K,p as F,q as B}from"./index-DLSysrh4.js";import"./date-range-picker-CoReVTN3.js";import{u as je,a as c,b as i,d as r,e as d,F as ge,c as _,L as U}from"./form-DCttU9FS.js";import{a as le}from"./pos-api-CYJtTNYA.js";import{I as w}from"./input-42MGwRgA.js";import{T as Te}from"./textarea-DA1C5L_X.js";import{R as D,a as z}from"./radio-group-CIC0p8IJ.js";import{C as V}from"./combobox-Cw6fZwGg.js";import{s as Ie,v as ve,S as M,O as Oe,b as Ve,R as Pe}from"./revenue-lock-section-DCMEFJa5.js";import{v as be}from"./vietqr-api-CdtuA3dS.js";import{D as ee,a as se,b as ce,c as ie,g as Ae,e as Ne}from"./dialog-DGVckEWq.js";import{t as Me,f as qe,z as Ee}from"./isSameMonth-C8JQo-AN.js";import{u as re}from"./useQuery-w3l-JMwb.js";import{Q as W}from"./query-keys-xK68hCS0.js";import{c as ye}from"./createLucideIcon-BCCeA4NW.js";import{T as Re,a as Ke,b as ne,c as Y,d as Fe,e as Q}from"./table-yR0osUgs.js";import{s as fe}from"./zod-j2m7p1zB.js";import{T as Be}from"./trash-2-C1HJm0c9.js";import{C as H}from"./circle-help-Bn10grtQ.js";import{I as te}from"./image-DJbQr-DC.js";import{P as ae}from"./plus-4pOKdB3b.js";import{u as ke}from"./useMutation-DGZ6hM-V.js";import"./user-QGmeemH2.js";import"./crm-api-B_UJzTcg.js";import{u as Ce}from"./use-source-data-CoK-Bjz_.js";import{P as He}from"./modal-BEyJXC3x.js";import{C as g}from"./checkbox-C0erTYQj.js";import{C as he}from"./chevron-right-CzKIkqDA.js";import{C as J,f as xe}from"./select-LfFpGbbb.js";import{u as Qe}from"./use-roles-j-qQTLth.js";import{b as Ue}from"./use-devices-DIrYI9xo.js";import{b as we}from"./stores-api-SKuCFAOT.js";import{d as Xe}from"./use-stores-FXDW9bYw.js";import{T as Ge}from"./triangle-alert-DTw360BJ.js";import{X as Ye}from"./calendar-DaAHj6Vo.js";/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],$e=ye("map-pin",ze);/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],We=ye("refresh-cw",Le);function Je(s,n){return Me(s*1e3,n==null?void 0:n.in)}const Ze=()=>re({queryKey:[W.VIETQR_BANKS],queryFn:async()=>(await be.getBanks()).data||[],staleTime:24*60*60*1e3,gcTime:24*60*60*1e3,retry:3,retryDelay:s=>Math.min(1e3*2**s,3e4)}),De=()=>ke({mutationFn:async s=>await be.generateQuickLink(s),onSuccess:()=>{P.success("Tạo mã QR thành công!")},onError:s=>{var t,a;const n=((a=(t=s==null?void 0:s.response)==null?void 0:t.data)==null?void 0:a.message)||"Có lỗi xảy ra khi tạo mã QR";P.error(n)}}),L={store_name:"",description:"",sale_change_vat_enable:1,value_vat:"",vat_discount_config:"",vat_discount_configs:[],print_bill_split:0,invoice_output:"",net_work:"0",address:"",latitude:0,longitude:0,city_uid:"",phone:"",email:"",facebook:"",website:"",logo:"",background:"",secondary_screen_image:"",secondary_screen_video:"",auto_check_momo_aio:0,print_type:"PROVISIONAL_INVOICE",group_item:!1,bill_template:0,prevent_cashier_edit_printer:!1,disable_print_button_in_payment_screen:0,is_show_logo_in_provisional_invoice:!1,report_item_combo_split:0,tem_invoice_fake_bill:!1,hideItemPriceAfterPrintBill:!1,hideItemPriceAfterPrintChecklist:!1,require_pin_reprint:!1,prevent_print_order_transfer:!1,allway_show_tag_so:!1,use_shift_pos:!1,counter_code:"",counter_mails:[],sources_print:[],print_bill_order_area:!1,is_ahamove_active:!1,phone_manager:"",ahamove_payment_method:"",ahamove_voucher_default:"",operate_model:0,exchange_points_for_voucher:!1,view_voucher_of_member:!1,enable_checkin_by_phone_number:!1,multi_voucher:!1,find_member:!1,is_run_buffet:!1,require_buffet_item:!1,enable_count_money:!1,disable_shift_total_amount:0,allow_remove_shift_open:!1,close_shift_auto_logout:!1,require_close_shift_in_day:!1,discount_reverse_on_price:!1,inv_skip_item_no_price:!1,auto_export_vat:!1,export_time_vat:"",require_vat_info:!1,pm_export_vat:!1,bill_auto_export_vat:!1,sorted_by_print:!1,enable_cash_drawer:!1,confirm_request:!1,use_order_control:!1,enable_tab_delivery:!1,enable_note_delete_item:!1,service_charge_optional:!1,require_peo_count:!1,require_confirm_merge_table:!1,hide_peo_count:!1,enable_edit_item_price_while_selling:0,role_quick_login:"",auto_confirm_o2o_post_paid:!1,resetItemOutOfStockStatus:!1,resetItemQuantityNewDay:!1,pin_code:"",time_out_use_pin:0,open_at:0,tracking_sale:0,enable_turn_order_report:!1,change_log_detail:!1,enable_change_item_in_store:!1,enable_change_item_type_in_store:!1,enable_change_printer_position_in_store:!1,prevent_create_custom_item:!1,require_custom_item_vat:!1,require_category_for_custom_item:!1,is_menu_by_source:!1,tran_no_syn_order:0,enable_tran_no_prefix:!1,tran_no_prefix:"",reset_tran_no_period:"DAILY",sources_not_print:[],print_order_at_checkout:!1,print_label_at_checkout:!1,sources_label_print:[],allow_printer_for_invoice_by_location:!1,split_combo:!1,ignore_combo_note:!1,show_item_price_zero:!1,enable_delete_order_bill:!1,print_item_switch_table:!1,fb_store_id:"",partner_id:"",license_expiry:"",license_package:"",payment_lock_time:"",store_id:"",no_kick_pda:!1,device_receive_online:"",active_devices:[],time_after_lock:0,time_lock_data:0,bank_id:"",bank_acc:"",bank_acc_name:"",bank_name:"",print_qrcode_pay:0,source_ids_selected:[]},es=[{value:1,label:"VAT xuôi"},{value:2,label:"VAT ngược"},{value:3,label:"VAT xuôi và hỗ trợ tính VAT trên từng món"},{value:4,label:"VAT ngược và hỗ trợ tính VAT trên từng món"},{value:5,label:"VAT cho hộ kinh doanh cá thể"},{value:6,label:"Không tính VAT"}],ss=[{value:0,label:"In cả hóa đơn tổng và hóa đơn tách"},{value:1,label:"Chỉ in hóa đơn tổng"},{value:2,label:"Chỉ in hóa đơn tách"},{value:3,label:"Không tự động tách hóa đơn khi thanh toán"}],ns=[{value:"",label:"None"},{value:"FABI",label:"FABI (Lưu thông tin hoá đơn điện tử)"}],ts=[{value:"0",label:"Không in"},{value:"1",label:"Hiển thị mã VietQr khi in hóa đơn"},{value:"2",label:"Hiển thị mã VietQr khi in hóa đơn và tạm tính"},{value:"3",label:"Hiển thị mã VietQr khi in tạm tính"}],as=[{value:0,label:"Không cấu hình"},{value:1,label:"Quét QR trên phiếu tạm tính /PDA/ mã được in và gửi thông báo tới máy chủ khi thanh toán thành công"},{value:2,label:"Hiển thị mã MoMo QR Đa Năng khi in hóa đơn thanh toán tiền mặt, tự động thay đổi PTTT cho hóa đơn nếu khách hàng quét mã QR"}],ls=[{value:"PROVISIONAL_INVOICE",label:"In tạm tính"},{value:"CHECK_LIST",label:"In chốt đồ"},{value:"CHECK_LIST_AND_PROVISIONAL_INVOICE",label:"In chốt đồ & tạm tính"},{value:"NO_PRINT",label:"Không in tạm tính, chốt đồ"}],cs=[{value:"CASH",label:"Tiền mặt"},{value:"PREPAID",label:"Trả trước"}],is=[{value:"NO_LOCK",label:"Không khóa"},{value:"LOCK_5_MIN",label:"Khóa 5 phút"},{value:"LOCK_10_MIN",label:"Khóa 10 phút"},{value:"LOCK_15_MIN",label:"Khóa 15 phút"},{value:"LOCK_30_MIN",label:"Khóa 30 phút"}],rs=s=>{var t;const n=s.extra_data||{};return{...L,store_name:s.store_name||"",phone:s.phone||"",description:n.description||"",sale_change_vat_enable:n.sale_change_vat_enable||1,value_vat:n.value_vat||"",vat_discount_config:"",vat_discount_configs:[],print_bill_split:n.print_bill_split||0,invoice_output:"",net_work:n.net_work===1?"1":"0",address:s.address||"",city_uid:s.city_uid||"",email:s.email||"",facebook:n.facebook||"",website:n.website||"",logo:n.logo||"",background:s.background||"",secondary_screen_image:n.secondary_screen_image||"",secondary_screen_video:n.secondary_screen_video||"",latitude:s.latitude||0,longitude:s.longitude||0,bank_id:n.bank_id||"",bank_acc:n.bank_acc||"",bank_acc_name:n.bank_acc_name||"",bank_name:n.bank_name||"",print_qrcode_pay:n.print_qrcode_pay||0,group_item:n.group_item===1,bill_template:n.bill_template||0,prevent_cashier_edit_printer:n.prevent_cashier_edit_printer===1,disable_print_button_in_payment_screen:n.disable_print_button_in_payment_screen||0,is_show_logo_in_provisional_invoice:n.is_show_logo_in_provisional_invoice===1,report_item_combo_split:n.report_item_combo_split||0,tem_invoice_fake_bill:n.tem_invoice_fake_bill===1,hideItemPriceAfterPrintBill:n.hideItemPriceAfterPrintBill===1,hideItemPriceAfterPrintChecklist:n.hideItemPriceAfterPrintChecklist===1,require_pin_reprint:n.require_pin_reprint===1,prevent_print_order_transfer:n.prevent_print_order_transfer===1,allway_show_tag_so:n.allway_show_tag_so===1,use_shift_pos:n.use_shift_pos===1,counter_code:n.counter_code||"",counter_mails:n.counter_mails||[],auto_check_momo_aio:n.auto_check_momo_aio||0,print_type:n.print_type==="PROVISIONAL_INVOICE"?"PROVISIONAL_INVOICE":n.print_type==="CHECK_LIST_AND_PROVISIONAL_INVOICE"?"CHECK_LIST_AND_PROVISIONAL_INVOICE":n.print_type==="CHECK_LIST"?"CHECK_LIST":"NO_PRINT",print_limit:n.print_limit?n.print_limit.toString():"",sources_print:n.sources_print||[],print_bill_order_area:n.print_bill_order_area===1,is_ahamove_active:s.is_ahamove_active===1,phone_manager:s.phone_manager||"",ahamove_payment_method:n.ahamove_payment_method||"",ahamove_voucher_default:n.ahamove_voucher_default||"",operate_model:n.operate_model||0,exchange_points_for_voucher:n.exchange_points_for_voucher===1,view_voucher_of_member:n.view_voucher_of_member===1,enable_checkin_by_phone_number:n.enable_checkin_by_phone_number===1,multi_voucher:n.multi_voucher===1,find_member:n.find_member===1,is_run_buffet:n.is_run_buffet===1,require_buffet_item:n.require_buffet_item===1,enable_count_money:n.enable_count_money===1,disable_shift_total_amount:n.disable_shift_total_amount||0,allow_remove_shift_open:n.allow_remove_shift_open===1,close_shift_auto_logout:n.close_shift_auto_logout===1,require_close_shift_in_day:n.require_close_shift_in_day===1,discount_reverse_on_price:n.discount_reverse_on_price===1,inv_skip_item_no_price:n.inv_skip_item_no_price===1,auto_export_vat:n.auto_export_vat===1,bill_auto_export_vat:n.bill_auto_export_vat===1,sorted_by_print:n.sorted_by_print===1,export_time_vat:n.export_time_vat?n.export_time_vat.toString():"",require_vat_info:n.require_vat_info===1,pm_export_vat:n.pm_export_vat===1,partner_id:n.partner_id||"",print_item_switch_table:n.print_item_switch_table===1,time_after_lock:n.time_after_lock||0,time_lock_data:n.time_lock_data||0,source_ids_selected:n.source_ids_selected||[],enable_cash_drawer:n.enable_cash_drawer===1,confirm_request:n.confirm_request===1,use_order_control:n.use_order_control===1,enable_note_delete_item:n.enable_note_delete_item===1,service_charge_optional:n.service_charge_optional===1,require_peo_count:n.require_peo_count===1,enable_tab_delivery:n.enable_tab_delivery===1,require_confirm_merge_table:n.require_confirm_merge_table===1,hide_peo_count:n.hide_peo_count===1,enable_change_item_in_store:n.enable_change_item_in_store===1,enable_change_item_type_in_store:n.enable_change_item_type_in_store===1,enable_change_printer_position_in_store:n.enable_change_printer_position_in_store===1,prevent_create_custom_item:n.prevent_create_custom_item===1,require_category_for_custom_item:n.require_category_for_custom_item===1,is_menu_by_source:n.is_menu_by_source===1,enable_tran_no_prefix:n.enable_tran_no_prefix===1,tran_no_prefix:n.tran_no_prefix||"",sources_not_print:n.sources_not_print||[],allow_printer_for_invoice_by_location:n.allow_printer_for_invoice_by_location===1,split_combo:n.split_combo===1,ignore_combo_note:n.ignore_combo_note===1,show_item_price_zero:n.show_item_price_zero===1,enable_delete_order_bill:n.enable_delete_order_bill===1,fb_store_id:((t=s.fb_store_id)==null?void 0:t.toString())||"",license_expiry:n.license_expiry||"",license_package:n.license_package||"",payment_lock_time:n.time_lock_data===0?"NO_LOCK":n.time_lock_data===5?"LOCK_5_MIN":n.time_lock_data===10?"LOCK_10_MIN":n.time_lock_data===15?"LOCK_15_MIN":n.time_lock_data===30?"LOCK_30_MIN":"NO_LOCK",store_id:s.store_id||"",no_kick_pda:n.no_kick_pda===1,device_receive_online:n.device_receive_online||"",active_devices:n.active_devices||[],enable_edit_item_price_while_selling:n.enable_edit_item_price_while_selling||0,role_quick_login:n.role_quick_login||"",auto_confirm_o2o_post_paid:n.auto_confirm_o2o_post_paid===1,resetItemOutOfStockStatus:n.resetItemOutOfStockStatus===1,resetItemQuantityNewDay:n.resetItemQuantityNewDay===1,pin_code:n.pin_code||"",time_out_use_pin:n.time_out_use_pin||0,open_at:n.open_at||0,tracking_sale:n.tracking_sale||0,enable_turn_order_report:n.enable_turn_order_report===1,change_log_detail:n.change_log_detail===1,require_custom_item_vat:n.require_custom_item_vat===1,tran_no_syn_order:n.tran_no_syn_order||0,print_order_at_checkout:n.print_order_at_checkout===1,print_label_at_checkout:n.print_label_at_checkout===1,reset_tran_no_period:n.reset_tran_no_period||"DAILY",sources_label_print:n.sources_label_print||[],delivery_services:s.delivery_services||"",email_delivery_service:s.email_delivery_service||"",brand_uid:s.brand_uid||"",company_uid:s.company_uid||"",created_at:s.created_at,expiry_date:s.expiry_date,active:s.active,is_fabi:s.is_fabi}},de=({storeId:s,enabled:n=!0})=>{const{company:t,brands:a}=ue(b=>b.auth),h=a==null?void 0:a[0],o=re({queryKey:[W.STORES_DETAIL,s,t==null?void 0:t.id,h==null?void 0:h.id],queryFn:async()=>{var O;if(!(t!=null&&t.id)||!(h!=null&&h.id)||!s)return;const b=new URLSearchParams({company_uid:t.id,brand_uid:h.id,id:s}),S=await le.get(`/mdata/v1/store?${b.toString()}`);if((O=S.data)!=null&&O.data)return S.data.data},enabled:n&&!!s&&!!(t!=null&&t.id)&&!!(h!=null&&h.id),staleTime:15*60*1e3,gcTime:30*60*1e3}),m=o.data,x=m?new Date(m.expiry_date*1e3)<new Date:!1,l=(m==null?void 0:m.active)===1,u=m?rs(m):void 0,{isLoading:p,isFetching:f,isError:j,error:N,refetch:I,status:k,fetchStatus:v}=o;return{data:m,formValues:u,isExpired:x,isActive:l,isLoading:p,isFetching:f,isError:j,error:N,refetch:I,status:k,fetchStatus:v}},ds=({storeId:s,initialData:n}={})=>{const t=!!s,{formValues:a,isLoading:h}=de({storeId:s,enabled:t}),o=y.useRef(!1),m=je({resolver:fe(Ie),defaultValues:{...L,...n},mode:"onChange"});y.useEffect(()=>{o.current=!1},[s]),y.useEffect(()=>{t&&a&&!h&&!o.current&&(o.current=!0,m.reset({...L,...a,...n}))},[a,h,t,n,m]);const x=()=>{const f=t&&a?{...L,...a,...n}:{...L,...n};m.reset(f)},l=m.watch(),u=ve(l),p=m.formState.isValid;return{form:m,resetForm:x,isFormValid:p,isRequiredFieldsValid:u,isEditMode:t,isLoadingStoreData:h}},os=(s,n,t,a=[])=>({extra_data:{prevent_print_order_transfer:s.prevent_print_order_transfer?1:0,print_bill_order_area:s.print_bill_order_area?1:0,value_vat:s.value_vat||"0",sale_change_vat_enable:s.sale_change_vat_enable||0,device_receive_online:s.device_receive_online||"",print_type:s.print_type||"PROVISIONAL_INVOICE",net_work:s.net_work==="1"?1:0,enable_change_item_in_store:s.enable_change_item_in_store?1:0,subscription_info:{},enable_checkin_by_phone_number:s.enable_checkin_by_phone_number?1:0,ahamove_payment_method:s.ahamove_payment_method||"AHAMOVE",open_at:s.open_at||0,pin_code:s.pin_code||"",operate_model:s.operate_model||0,require_number_peo:s.require_peo_count?1:0,tracking_sale:s.tracking_sale||0,use_order_control:s.use_order_control?1:0,split_combo:s.split_combo?1:0,group_item_print:s.group_item?1:0,bill_template:s.bill_template||0,show_item_price_zero:s.show_item_price_zero?1:0,tran_no_syn_order:s.tran_no_syn_order||0,disable_shift_total_amount:s.disable_shift_total_amount||0,allow_remove_shift_open:s.allow_remove_shift_open?1:0,require_close_shift_in_day:s.require_close_shift_in_day?1:0,inv_skip_item_no_price:s.inv_skip_item_no_price?1:0,bill_auto_export_vat:s.bill_auto_export_vat?1:0,sorted_by_print:s.sorted_by_print?1:0,confirm_request:s.confirm_request?1:0,require_peo_count:s.require_peo_count?1:0,require_confirm_merge_table:s.require_confirm_merge_table?1:0,hide_peo_count:s.hide_peo_count?1:0,resetItemOutOfStockStatus:s.resetItemOutOfStockStatus?1:0,resetItemQuantityNewDay:s.resetItemQuantityNewDay?1:0,change_log_detail:s.change_log_detail?1:0,require_custom_item_vat:s.require_custom_item_vat?1:0,require_category_for_custom_item:s.require_category_for_custom_item?1:0,print_order_at_checkout:s.print_order_at_checkout?1:0,print_label_at_checkout:s.print_label_at_checkout?1:0,only_print_label_ta:s.print_label_at_checkout?1:0,prevent_create_custom_item:s.prevent_create_custom_item?1:0,is_menu_by_source:s.is_menu_by_source?1:0,prevent_cashier_edit_printer:s.prevent_cashier_edit_printer?1:0,exchange_points_for_voucher:s.exchange_points_for_voucher?1:0,view_voucher_of_member:s.view_voucher_of_member?1:0,enable_edit_item_price_while_selling:s.enable_edit_item_price_while_selling||0,discount_reverse_on_price:s.discount_reverse_on_price?1:0,enable_cash_drawer:s.enable_cash_drawer?1:0,auto_export_vat:s.auto_export_vat?1:0,time_out_use_pin:s.time_out_use_pin||0,disable_print_button_in_payment_screen:s.disable_print_button_in_payment_screen||0,ahamove_voucher_default:s.ahamove_voucher_default||"",enable_delete_order_bill:s.enable_delete_order_bill?1:0,is_show_logo_in_provisional_invoice:s.is_show_logo_in_provisional_invoice?1:0,enable_tab_delivery:s.enable_tab_delivery?1:0,enable_turn_order_report:s.enable_turn_order_report?1:0,report_item_combo_split:s.report_item_combo_split||0,print_bill_split:s.print_bill_split||3,enable_tran_no_prefix:s.enable_tran_no_prefix?1:0,tran_no_prefix:s.tran_no_prefix||"",reset_tran_no_period:s.reset_tran_no_period||"DAILY",enable_note_delete_item:s.enable_note_delete_item?1:0,service_charge_optional:s.service_charge_optional?1:0,enable_minvoice:0,inv_buyerTaxCode:"",mau_hd:"",inv_invoiceSeries:"",inv_circulars:"",minvoice_link_api:"",minvoice_token:"",tem_invoice_fake_bill:s.tem_invoice_fake_bill?1:0,bill_not_show_item_zero:s.hideItemPriceAfterPrintBill?1:0,require_pin_reprint:s.require_pin_reprint?1:0,allway_show_tag_so:s.allway_show_tag_so?1:0,use_shift_pos:s.use_shift_pos?1:0,enable_count_money:s.enable_count_money?1:0,partner_id:s.partner_id||"",payment_method_aeon_selected:["ATM","VISA","MASTER"],has_aeon_export:0,bank_id:s.bank_id||"",bank_name:s.bank_name||"",bank_acc:s.bank_acc||"",bank_acc_name:s.bank_acc_name||"",print_qrcode_pay:s.print_qrcode_pay||0,is_run_buffet:s.is_run_buffet?1:0,require_buffet_item:s.require_buffet_item?1:0,export_time_vat:parseInt(s.export_time_vat||"0"),require_vat_info:s.require_vat_info?1:0,sources_label_print:s.sources_label_print||[],pm_export_vat:s.pm_export_vat?1:0,counter_code:s.counter_code||"",role_quick_login:s.role_quick_login||"",time_after_lock:s.time_after_lock||0,time_lock_data:s.time_lock_data||0,print_item_switch_table:s.print_item_switch_table?1:0,no_kick_pda:s.no_kick_pda?1:0,close_shift_auto_logout:s.close_shift_auto_logout?1:0,discounts_vat_hkd:[],multi_voucher:s.multi_voucher?1:0,find_member:s.find_member?1:0,sources_print:s.sources_print||[],enable_change_item_type_in_store:s.enable_change_item_type_in_store?1:0,enable_change_printer_position_in_store:s.enable_change_printer_position_in_store?1:0,sources_not_print:s.sources_not_print||[],auto_check_momo_aio:s.auto_check_momo_aio||0,auto_confirm_o2o_post_paid:s.auto_confirm_o2o_post_paid?1:0,new_ui_payment_momo:1,print_limit:parseInt(s.print_limit||"0"),option_export_vat_pos_or_cms:0,state_print_order:s.print_order_at_checkout?1:0,last_confirm_use_multi_voucher_at:null,secondary_screen_video:s.secondary_screen_video||"",secondary_screen_image:s.secondary_screen_image||"",counter_mails:s.counter_mails||[]},source_ids_selected:s.sources_print||[],store_name:s.store_name,phone:s.phone,city_uid:s.city_uid,address:s.address,email:s.email||"",background:s.background||"",phone_manager:s.phone_manager||"",is_ahamove_active:s.is_ahamove_active?1:0,company_uid:n,brand_uid:t,delivery_services:s.is_ahamove_active?"AHAMOVE":"",longitude:s.longitude||0,latitude:s.latitude||0}),hs=(s,n)=>({...n,store_name:s.store_name,address:s.address,phone:s.phone,email:s.email||"",latitude:s.latitude||0,longitude:s.longitude||0,phone_manager:s.phone_manager||"",is_ahamove_active:s.is_ahamove_active?1:0,city_uid:s.city_uid,background:s.background||n.background||"",source_ids_selected:s.source_ids_selected||[],extra_data:{...n.extra_data,facebook:s.facebook||"",website:s.website||"",logo:s.logo||"",background:s.background||"",description:s.description||"",value_vat:s.value_vat||"",sale_change_vat_enable:s.sale_change_vat_enable||1,print_type:s.print_type||"",print_bill_split:s.print_bill_split||0,net_work:s.net_work||"0",enable_change_item_in_store:s.enable_change_item_in_store?1:0,enable_checkin_by_phone_number:s.enable_checkin_by_phone_number?1:0,ahamove_payment_method:s.ahamove_payment_method||"",ahamove_voucher_default:s.ahamove_voucher_default||"",disable_shift_total_amount:s.disable_shift_total_amount||0,allow_remove_shift_open:s.allow_remove_shift_open?1:0,require_close_shift_in_day:s.require_close_shift_in_day?1:0,inv_skip_item_no_price:s.inv_skip_item_no_price?1:0,bill_auto_export_vat:s.bill_auto_export_vat?1:0,sorted_by_print:s.sorted_by_print?1:0,confirm_request:s.confirm_request?1:0,require_peo_count:s.require_peo_count?1:0,require_confirm_merge_table:s.require_confirm_merge_table?1:0,hide_peo_count:s.hide_peo_count?1:0,resetItemOutOfStockStatus:s.resetItemOutOfStockStatus?1:0,resetItemQuantityNewDay:s.resetItemQuantityNewDay?1:0,change_log_detail:s.change_log_detail?1:0,require_custom_item_vat:s.require_custom_item_vat?1:0,require_category_for_custom_item:s.require_category_for_custom_item?1:0,tran_no_syn_order:s.tran_no_syn_order||0,print_order_at_checkout:s.print_order_at_checkout?1:0,print_label_at_checkout:s.print_label_at_checkout?1:0,print_bill_order_area:s.print_bill_order_area?1:0,ignore_combo_note:s.ignore_combo_note?1:0,sources_label_print:s.sources_label_print||[],group_item:s.group_item?1:0,bill_template:s.bill_template||0,prevent_cashier_edit_printer:s.prevent_cashier_edit_printer?1:0,disable_print_button_in_payment_screen:s.disable_print_button_in_payment_screen||0,is_show_logo_in_provisional_invoice:s.is_show_logo_in_provisional_invoice?1:0,report_item_combo_split:s.report_item_combo_split||0,tem_invoice_fake_bill:s.tem_invoice_fake_bill?1:0,hideItemPriceAfterPrintBill:s.hideItemPriceAfterPrintBill?1:0,hideItemPriceAfterPrintChecklist:s.hideItemPriceAfterPrintChecklist?1:0,require_pin_reprint:s.require_pin_reprint?1:0,prevent_print_order_transfer:s.prevent_print_order_transfer?1:0,allway_show_tag_so:s.allway_show_tag_so?1:0,use_shift_pos:s.use_shift_pos?1:0,counter_mails:s.counter_mails||[],open_at:s.open_at||0,pin_code:s.pin_code||"",operate_model:s.operate_model||0,tracking_sale:s.tracking_sale||0,use_order_control:s.use_order_control?1:0,split_combo:s.split_combo?1:0,show_item_price_zero:s.show_item_price_zero?1:0,prevent_create_custom_item:s.prevent_create_custom_item?1:0,is_menu_by_source:s.is_menu_by_source?1:0,exchange_points_for_voucher:s.exchange_points_for_voucher?1:0,view_voucher_of_member:s.view_voucher_of_member?1:0,enable_edit_item_price_while_selling:s.enable_edit_item_price_while_selling||0,discount_reverse_on_price:s.discount_reverse_on_price?1:0,enable_cash_drawer:s.enable_cash_drawer?1:0,auto_export_vat:s.auto_export_vat?1:0,time_out_use_pin:s.time_out_use_pin||0,enable_delete_order_bill:s.enable_delete_order_bill?1:0,enable_tab_delivery:s.enable_tab_delivery?1:0,enable_turn_order_report:s.enable_turn_order_report?1:0,enable_tran_no_prefix:s.enable_tran_no_prefix?1:0,tran_no_prefix:s.tran_no_prefix||"",reset_tran_no_period:s.reset_tran_no_period||"DAILY",enable_note_delete_item:s.enable_note_delete_item?1:0,service_charge_optional:s.service_charge_optional?1:0,bill_not_show_item_zero:s.show_item_price_zero?1:0,enable_count_money:s.enable_count_money?1:0,bank_id:s.bank_id||"",bank_acc:s.bank_acc||"",bank_acc_name:s.bank_acc_name||"",bank_name:s.bank_name||"",print_qrcode_pay:s.print_qrcode_pay||"",is_run_buffet:s.is_run_buffet?1:0,require_buffet_item:s.require_buffet_item?1:0,export_time_vat:parseInt(s.export_time_vat||"0"),require_vat_info:s.require_vat_info?1:0,pm_export_vat:s.pm_export_vat?1:0,counter_code:s.counter_code||"",partner_id:s.partner_id||"",print_item_switch_table:s.print_item_switch_table?1:0,close_shift_auto_logout:s.close_shift_auto_logout?1:0,multi_voucher:s.multi_voucher?1:0,find_member:s.find_member?1:0,enable_change_item_type_in_store:s.enable_change_item_type_in_store?1:0,enable_change_printer_position_in_store:s.enable_change_printer_position_in_store?1:0,sources_not_print:s.sources_not_print||[],auto_check_momo_aio:s.auto_check_momo_aio||0,auto_confirm_o2o_post_paid:s.auto_confirm_o2o_post_paid?1:0,new_ui_payment_momo:s.auto_check_momo_aio===1?1:0,print_limit:parseInt(s.print_limit||"0"),secondary_screen_video:s.secondary_screen_video||"",secondary_screen_image:s.secondary_screen_image||"",role_quick_login:s.role_quick_login||"",time_after_lock:s.time_after_lock||0,time_lock_data:s.time_lock_data||0,no_kick_pda:s.no_kick_pda?1:0,device_receive_online:s.device_receive_online||"",sources_print:s.sources_print||[]}}),xs=({formData:s,storeId:n,onSuccess:t})=>{const a=pe(),{company:h,brands:o}=ue(j=>j.auth),{createStore:m,isCreating:x}=Xe(),l=!!n,{data:u}=de({storeId:n,enabled:l}),p=()=>{a({to:M.ROUTE_STORE_LIST}).then(()=>{window.location.reload()})};return{handleBack:p,handleSave:()=>{var j;if(!ve(s)){P.error("Vui lòng điền đầy đủ: Tên điểm, Địa chỉ, Điện thoại, Thành phố");return}if(!(h!=null&&h.id)||!((j=o==null?void 0:o[0])!=null&&j.id)){P.error("Không tìm thấy thông tin công ty hoặc thương hiệu");return}if(l){if(!u){P.error("Không thể lấy dữ liệu cửa hàng hiện tại");return}const N=hs(s,u);we(N).then(()=>{P.success("Cập nhật cửa hàng thành công"),t==null||t(),p()}).catch(I=>{P.error(I.message||"Có lỗi xảy ra khi cập nhật cửa hàng")})}else{const N=os(s,h.id,o[0].id,s.sources_print);m(N,{onSuccess:()=>{t==null||t(),p()}})}},isEditMode:l,isCreating:x}},_s=({onSuccess:s}={})=>{const n=Se(),t=pe(),{mutate:a,isPending:h}=ke({mutationFn:async m=>we(m),onSuccess:(m,x)=>{n.invalidateQueries({queryKey:[W.STORES_LIST]}),n.invalidateQueries({queryKey:[W.STORES_DETAIL]});const l=x.active===1?"kích hoạt":"vô hiệu hóa";P.success(`Đã ${l} cửa hàng "${x.store_name}" thành công`),t({to:"/setting/store"}).then(()=>{window.location.reload()}),s==null||s()},onError:m=>{P.error(m.message||"Có lỗi xảy ra khi cập nhật trạng thái cửa hàng")}});return{handleToggleStatus:m=>{const x={...m,active:m.active===1?0:1};a(x)},isUpdating:h}},ms=async s=>(await le.post("/mdata/v1/store/sync-expiry-time-proactive",s)).data;function us({form:s,companyUid:n,brandUid:t,storeId:a}){const[h,o]=y.useState(!1);return{checkLicense:async()=>{const x=n||"595e8cb4-674c-49f7-adec-826b211a7ce3",l=t||"d43a01ec-2f38-4430-a7ca-9b3324f7d39e",u=a||"ba6e0a44-080d-4ae4-aba0-c29b79e95ab3";try{o(!0);const p=await ms({company_uid:x,brand_uid:l,id:u});return p&&p.expiry_time&&s.setValue("license_expiry",p.expiry_time),p}finally{o(!1)}},isCheckingLicense:h}}function ps({open:s,onOpenChange:n,licenseData:t}){if(!t)return null;const a=h=>{switch(h){case"active":return e.jsx("span",{className:"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800",children:"ACTIVE"});case"inactive":return e.jsx("span",{className:"inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800",children:"INACTIVE"});case"expired":return e.jsx("span",{className:"inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800",children:"EXPIRED"});default:return h}};return e.jsx(ee,{open:s,onOpenChange:n,children:e.jsxs(se,{className:"max-w-md",children:[e.jsx(ce,{children:e.jsx(ie,{children:"Thông tin gói bản quyền"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Cửa hàng"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.storeName})]}),e.jsxs("div",{children:[e.jsx("label",{className:"col-end-1 text-sm font-medium text-gray-600",children:"Trạng thái"}),e.jsx("div",{className:"mt-1",children:a(t.status)})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Ngày bắt đầu"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.startDate})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Ngày kết thúc"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.endDate})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Loại"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.type})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Product Code"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.productCode})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-600",children:"Service Code"}),e.jsx("p",{className:"text-sm text-gray-900",children:t.serviceCode})]})]})]})})}const _e=(s,n="dd/MM/yyyy HH:mm")=>{const t=typeof s=="string"?parseInt(s,10):typeof s=="number"?s:NaN;if(!Number.isFinite(t)||t<=0)return"";const a=t>1e12?Math.floor(t/1e3):t,h=Je(a);return Ee(h)?qe(h,n):""};function js({form:s,isLoading:n=!1,mode:t}){const{checkLicense:a,isCheckingLicense:h}=us({form:s}),[o,m]=y.useState(!1),x={storeName:s.getValues("store_name")||"",status:s.getValues("active")===1?"active":"inactive",startDate:_e(s.getValues("created_at")),endDate:_e(s.getValues("expiry_date")),type:"Store",productCode:s.getValues("is_fabi")===1?"FABI":"",serviceCode:"FABI_SUB"},l=async()=>{try{await a(),P.success("Kiểm tra bản quyền thành công")}catch{P.error("Kiểm tra bản quyền thất bại")}},u=()=>{m(!0)};return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Email"})}),e.jsx("div",{className:"col-span-3",children:e.jsx(c,{control:s.control,name:"email",render:({field:p})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{type:"email",placeholder:"Nhập địa chỉ email",disabled:n,...p})}),e.jsx(d,{})]})})}),e.jsx("div",{className:"col-span-2 pt-2",children:e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Điện thoại ",e.jsx("span",{className:"text-red-500",children:"*"})]})}),e.jsx("div",{className:"col-span-4",children:e.jsx(c,{control:s.control,name:"phone",render:({field:p})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập số điện thoại",disabled:n,...p})}),e.jsx(d,{})]})})})]}),t!=="add"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Foodbook Store ID"})}),e.jsx("div",{className:"col-span-3",children:e.jsx(c,{control:s.control,name:"fb_store_id",render:({field:p})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"123939",disabled:!0,...p})}),e.jsx(d,{})]})})}),e.jsx("div",{className:"col-span-2 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Mã thương hiệu"})}),e.jsx("div",{className:"col-span-4",children:e.jsx(c,{control:s.control,name:"partner_id",render:({field:p})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"BRAND-953H",disabled:!0,...p})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Thời hạn bản quyền"})}),e.jsx("div",{className:"col-span-9",children:e.jsxs("div",{className:"flex w-full gap-2",children:[e.jsx(c,{control:s.control,name:"license_expiry",render:({field:p})=>e.jsxs(i,{className:"flex-1",children:[e.jsx(r,{children:e.jsx(w,{placeholder:"18/08/2025 23:59",disabled:!0,...p,className:"w-full"})}),e.jsx(d,{})]})}),e.jsx(T,{type:"button",variant:"outline",size:"sm",onClick:l,disabled:n||h,children:h?"Đang kiểm tra...":"Kiểm tra"})]})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Gói bản quyền"})}),e.jsx("div",{className:"col-span-4",children:e.jsx(T,{type:"button",variant:"link",className:"h-auto p-0 text-blue-600 hover:text-blue-800",onClick:u,disabled:n,children:"Xem chi tiết"})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Thời gian khóa PTTT chuyển khoản"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"payment_lock_time",render:({field:p})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:is,value:p.value,onValueChange:p.onChange,placeholder:"Chọn thời gian khóa",searchPlaceholder:"Tìm kiếm...",emptyText:"Không tìm thấy tùy chọn nào.",disabled:!0,className:"w-full"})}),e.jsx(d,{})]})})})]})]}),t==="edit"&&e.jsx(ps,{open:o,onOpenChange:m,licenseData:x})]})}const gs=async()=>{try{const s=await le.get("/v1/mdata/cities");if(Array.isArray(s.data))return s.data.filter(t=>t.active===1);if(s.data.success===!1)throw new Error(s.data.message||"Failed to fetch cities");if(s.data.data&&Array.isArray(s.data.data))return s.data.data.filter(t=>t.active===1);throw new Error("Unexpected response structure")}catch(s){throw s instanceof Error?new Error(`Failed to fetch cities: ${s.message}`):new Error("Failed to fetch cities")}};function vs(){return re({queryKey:[W.CITIES],queryFn:gs,staleTime:5*60*1e3,gcTime:10*60*1e3})}function bs({value:s,onChange:n,onAddressSelect:t,placeholder:a="Nhập địa chỉ",disabled:h=!1,className:o}){const[m,x]=y.useState([]),[l,u]=y.useState(!1),[p,f]=y.useState(!1),[j,N]=y.useState(null),I=async b=>{if(b.length<3){x([]),u(!1);return}f(!0);try{const O=await(await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(b)}&accept-language=vi&limit=5&countrycodes=VN`)).json();x(O),u(!0)}catch(S){console.error("Error searching addresses:",S),x([])}finally{f(!1)}},k=b=>{n(b),j&&clearTimeout(j);const S=setTimeout(()=>{I(b)},500);N(S)},v=b=>{const S=parseFloat(b.lat),O=parseFloat(b.lon);n(b.display_name),u(!1),x([]),t&&t(b.display_name,S,O)};return y.useEffect(()=>()=>{j&&clearTimeout(j)},[j]),e.jsxs("div",{className:`relative ${o||""}`,onBlur:()=>{setTimeout(()=>u(!1),150)},children:[e.jsx(w,{value:s,onChange:b=>k(b.target.value),onFocus:()=>{m.length>0&&u(!0)},placeholder:a,disabled:h,className:"w-full",autoComplete:"off"}),p&&e.jsx("div",{className:"absolute top-1/2 right-3 -translate-y-1/2",children:e.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"})}),l&&m.length>0&&e.jsx("div",{className:"absolute top-full z-50 mt-1 max-h-60 w-full overflow-auto rounded-md border border-gray-200 bg-white shadow-lg",children:m.map((b,S)=>e.jsx("div",{className:"cursor-pointer px-3 py-2 text-sm hover:bg-gray-100",onClick:()=>v(b),children:b.display_name},S))})]})}function Ns({isVisible:s}){return s?e.jsx("div",{className:"mb-6 rounded-lg bg-red-500 px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"h-5 w-5"}),e.jsx("span",{className:"font-medium",children:M.EXPIRED_LICENSE})]})}):null}function ys({title:s,onBack:n,children:t}){return e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx(T,{variant:"ghost",size:"sm",onClick:n,className:"flex items-center",children:e.jsx(Ye,{className:"h-4 w-4"})}),t&&e.jsx("div",{className:"flex items-center gap-3",children:t})]}),e.jsx("div",{className:"text-center",children:e.jsx("h1",{className:"mb-2 text-3xl font-bold",children:s})})]})}function fs({isEditMode:s,isExpired:n,isActive:t,isUpdating:a,onToggleStatus:h,isRequiredFieldsValid:o,isCreating:m,onSave:x}){const l=s,u=!s||!n;if(!l&&!u)return null;const p=t?"destructive":"default",f=a?M.LOADING_UPDATING:t?M.BUTTON_DEACTIVE:M.BUTTON_ACTIVE,j=t?"":"bg-green-600 text-white hover:bg-green-700",N=m?M.LOADING_CREATING:M.BUTTON_SAVE;return e.jsxs(e.Fragment,{children:[l&&e.jsx(T,{type:"button",variant:p,disabled:a,className:`min-w-[${M.MIN_BUTTON_WIDTH}px] ${j}`,onClick:h,children:f}),u&&e.jsx(T,{type:"button",disabled:!o||m,className:`min-w-[${M.MIN_BUTTON_WIDTH}px]`,onClick:x,children:N})]})}function ks({form:s,isLoading:n=!1,isMapModalOpen:t,setIsMapModalOpen:a}){const{data:h=[],isLoading:o,error:m}=vs(),x=h.map(l=>({value:l.id,label:l.city_name}));return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Địa chỉ ",e.jsx("span",{className:"text-red-500",children:"*"})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"address",render:({field:l})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(bs,{value:l.value||"",onChange:l.onChange,onAddressSelect:(u,p,f)=>{l.onChange(u),s.setValue("latitude",p),s.setValue("longitude",f)},placeholder:"Nhập địa chỉ",disabled:n,className:"flex-1"}),e.jsxs(ee,{open:t,onOpenChange:a,children:[e.jsx(Ae,{asChild:!0,children:e.jsx(T,{type:"button",variant:"outline",size:"icon",disabled:n,className:"shrink-0",children:e.jsx($e,{className:"h-4 w-4"})})}),e.jsx(se,{className:"flex h-[700px] flex-col",style:{width:"80vw",maxWidth:"1000px"},children:e.jsx("div",{className:"flex-1 overflow-hidden",children:e.jsx(Oe,{defaultAddress:l.value,onAddressSelect:(u,p,f)=>{l.onChange(u);const j=s.getValues("latitude"),N=s.getValues("longitude");j!==void 0&&s.setValue("latitude",p),N!==void 0&&s.setValue("longitude",f),a(!1)},onCancel:()=>a(!1)})})})]})]})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Latitude"})}),e.jsx("div",{className:"col-span-4",children:e.jsx(c,{control:s.control,name:"latitude",render:({field:l})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{type:"number",step:"any",placeholder:"0",readOnly:!0,disabled:n,...l,className:"bg-gray-50"})}),e.jsx(d,{})]})})}),e.jsx("div",{className:"col-span-1 pt-2 text-center",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Longitude"})}),e.jsx("div",{className:"col-span-4",children:e.jsx(c,{control:s.control,name:"longitude",render:({field:l})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{type:"number",step:"any",placeholder:"0",readOnly:!0,disabled:n,...l,className:"bg-gray-50"})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Thành phố ",e.jsx("span",{className:"text-red-500",children:"*"})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"city_uid",render:({field:l})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:x,value:l.value,onValueChange:l.onChange,placeholder:o?"Đang tải thành phố...":"Chọn thành phố",searchPlaceholder:"Tìm kiếm thành phố...",emptyText:o?"Đang tải...":m?"Lỗi tải dữ liệu thành phố":"Không tìm thấy thành phố nào.",disabled:n||o,className:"w-full"})}),e.jsx(d,{})]})})})]})]})}function Cs({isOpen:s,onClose:n,onSave:t,editingConfig:a}){const h=je({resolver:fe(Ve),defaultValues:{discountPercentage:(a==null?void 0:a.discountPercentage)||0,vatPercentage:(a==null?void 0:a.vatPercentage)||0,programName:(a==null?void 0:a.programName)||"",startDate:(a==null?void 0:a.startDate)||new Date().toISOString().split("T")[0],endDate:(a==null?void 0:a.endDate)||new Date().toISOString().split("T")[0]}}),o=x=>{const l={id:(a==null?void 0:a.id)||Date.now().toString(),...x};t(l),h.reset(),n()},m=()=>{h.reset(),n()};return e.jsx(ee,{open:s,onOpenChange:m,children:e.jsxs(se,{className:"max-h-[80vh] w-[60vw] !max-w-[95vw]",children:[e.jsx(ce,{children:e.jsx(ie,{children:"Cấu hình Giảm giá VAT theo nghị định"})}),e.jsx(ge,{...h,children:e.jsxs("form",{onSubmit:h.handleSubmit(o),className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(c,{control:h.control,name:"startDate",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[200px] flex-shrink-0",children:"Ngày bắt đầu"}),e.jsx(r,{className:"flex-1",children:e.jsx(w,{type:"date",...x})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:h.control,name:"endDate",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[200px] flex-shrink-0",children:"Ngày kết thúc"}),e.jsx(r,{className:"flex-1",children:e.jsx(w,{type:"date",...x})})]}),e.jsx(d,{})]})})]}),e.jsx(c,{control:h.control,name:"discountPercentage",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"w-[200px] flex-shrink-0",children:["Mức giảm (%) ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(r,{className:"flex-1",children:e.jsx(w,{type:"number",placeholder:"0",...x,onChange:l=>x.onChange(Number(l.target.value))})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:h.control,name:"vatPercentage",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"w-[200px] flex-shrink-0",children:["VAT (%) ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(r,{className:"flex-1",children:e.jsx(w,{type:"number",placeholder:"0",...x,onChange:l=>x.onChange(Number(l.target.value))})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:h.control,name:"programName",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"w-[200px] flex-shrink-0",children:["Tên chương trình chiến dịch ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(r,{className:"flex-1",children:e.jsx(w,{placeholder:"Nhập tên chương trình",...x})})]}),e.jsx(d,{})]})}),e.jsxs(Ne,{children:[e.jsx(T,{type:"button",variant:"outline",onClick:m,children:"Hủy"}),e.jsx(T,{type:"submit",children:"Lưu"})]})]})})]})})}function ws({isOpen:s,onClose:n,configs:t,onConfigsChange:a}){const[h,o]=y.useState(!1),[m,x]=y.useState(null),l=()=>{x(null),o(!0)},u=j=>{const N=t.filter(I=>I.id!==j);a(N)},p=j=>{if(m){const N=t.map(I=>I.id===m.id?j:I);a(N)}else a([...t,j]);o(!1),x(null)},f=j=>new Date(j).toLocaleDateString("vi-VN");return e.jsxs(e.Fragment,{children:[e.jsx(ee,{open:s,onOpenChange:n,children:e.jsxs(se,{className:"max-h-[80vh] w-[60vw] !max-w-[95vw]",children:[e.jsx(ce,{children:e.jsx(ie,{children:"Cấu hình Giảm giá VAT theo nghị định"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex justify-end",children:e.jsx(T,{onClick:l,className:"bg-green-600 hover:bg-green-700",children:"Thêm cấu hình"})}),e.jsx("div",{className:"overflow-x-auto rounded-lg border",children:e.jsxs(Re,{className:"min-w-[800px]",children:[e.jsx(Ke,{children:e.jsxs(ne,{children:[e.jsx(Y,{className:"w-16",children:"STT"}),e.jsx(Y,{className:"w-32",children:"Mức giảm (%)"}),e.jsx(Y,{className:"w-32",children:"Thuế suất (%)"}),e.jsx(Y,{className:"min-w-[200px]",children:"Tên chương trình chiến dịch"}),e.jsx(Y,{className:"w-40",children:"Thời gian bắt đầu"}),e.jsx(Y,{className:"w-40",children:"Thời gian kết thúc"}),e.jsx(Y,{className:"w-16"})]})}),e.jsx(Fe,{children:t.length===0?e.jsx(ne,{children:e.jsx(Q,{colSpan:7,className:"py-8 text-center text-gray-500",children:"Chưa có cấu hình nào"})}):t.map((j,N)=>e.jsxs(ne,{children:[e.jsx(Q,{children:N+1}),e.jsx(Q,{children:j.discountPercentage}),e.jsx(Q,{children:j.vatPercentage}),e.jsx(Q,{children:j.programName}),e.jsx(Q,{children:f(j.startDate)}),e.jsx(Q,{children:f(j.endDate)}),e.jsx(Q,{children:e.jsx(T,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700",onClick:()=>u(j.id),children:e.jsx(Be,{className:"h-4 w-4"})})})]},j.id))})]})})]}),e.jsxs(Ne,{children:[e.jsx(T,{variant:"outline",onClick:n,children:"Hủy"}),e.jsx(T,{onClick:n,children:"Lưu"})]})]})}),e.jsx(Cs,{isOpen:h,onClose:()=>{o(!1),x(null)},onSave:p,editingConfig:m})]})}function Ss({form:s,isLoading:n=!1,mode:t}){const[a,h]=y.useState(!1),[o,m]=y.useState(!1),x=s.watch("sale_change_vat_enable"),l=s.watch("vat_discount_configs")||[],u=[1,2,3,4,5].includes(x),p=x===5,f=j=>{s.setValue("vat_discount_configs",j);const N=j.length>0?`${j.length} cấu hình`:"0 cấu hình";s.setValue("vat_discount_config",N)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Thông tin chi tiết"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Tên điểm ",e.jsx("span",{className:"text-red-500",children:"*"})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"store_name",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập tên điểm",disabled:n,...j})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Mô tả"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"description",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(Te,{placeholder:"Mô tả",disabled:n,...j})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Loại VAT"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"sale_change_vat_enable",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:es,value:j.value,onValueChange:N=>j.onChange(Number(N)),placeholder:"Tìm kiếm",searchPlaceholder:"Tìm kiếm loại VAT...",emptyText:"Không tìm thấy loại VAT nào.",disabled:n,className:"w-full"})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cấu hình in tách hóa đơn khi chọn vat theo món"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"print_bill_split",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:ss,value:j.value,onValueChange:N=>j.onChange(Number(N)),placeholder:"Chọn cấu hình in hóa đơn",searchPlaceholder:"Tìm kiếm cấu hình...",emptyText:"Không tìm thấy cấu hình nào.",disabled:n,className:"w-full"})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Đối tác xuất HĐĐT"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{className:"text-sm",children:"Mỗi cửa hàng chỉ chọn một bên kết nối. Mỗi hóa đơn chỉ xuất cho một bên đối tác."})})]})})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"partner_id",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:ns,value:j.value,onValueChange:j.onChange,placeholder:"Chọn đối tác xuất HĐDT",searchPlaceholder:"Tìm kiếm đối tác...",emptyText:"Không tìm thấy đối tác nào.",disabled:n,className:"w-full"})}),e.jsx(d,{})]})})})]})]}),u&&e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"VAT (%)"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"value_vat",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{type:"number",placeholder:"0",disabled:n,...j,onChange:N=>j.onChange(N.target.value)})}),e.jsx(d,{})]})})})]}),p&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cấu hình Giảm giá VAT theo nghị định"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"vat_discount_config",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(T,{type:"button",variant:"outline",className:"w-full justify-start text-left font-normal",onClick:()=>m(!0),disabled:n,children:j.value||`${l.length} cấu hình`})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"VAT (%)"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"value_vat",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{type:"number",placeholder:"0",disabled:n,...j,onChange:N=>j.onChange(N.target.value)})}),e.jsx(d,{})]})})})]})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Cấu hình kết nối thiết bị nội bộ qua mạng"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"net_work",render:({field:j})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx("div",{className:"flex h-9 items-center gap-6",children:e.jsxs(D,{onValueChange:j.onChange,defaultValue:j.value,className:"flex gap-6",disabled:n,children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{value:"0",id:"lan"}),e.jsx(U,{htmlFor:"lan",children:"LAN"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{value:"1",id:"lan-internet"}),e.jsx(U,{htmlFor:"lan-internet",children:"LAN + Internet"})]})]})})}),e.jsx(d,{})]})})})]}),e.jsx(ks,{form:s,isLoading:n,isMapModalOpen:a,setIsMapModalOpen:h}),e.jsx(js,{form:s,isLoading:n,mode:t}),e.jsx(ws,{isOpen:o,onClose:()=>m(!1),configs:l,onConfigsChange:f})]})}function Ts({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Thông tin khác"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Facebook"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"facebook",render:({field:t})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"Facebook",disabled:n,...t})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Website"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"website",render:({field:t})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"Website",disabled:n,...t})}),e.jsx(d,{})]})})})]})]})]})}function Is({form:s,isLoading:n=!1}){const[t,a]=y.useState(""),[h,o]=y.useState(""),[m,x]=y.useState(""),l=y.useRef(null),u=y.useRef(null),p=y.useRef(null),f=()=>{var b;(b=l.current)==null||b.click()},j=()=>{var b;(b=u.current)==null||b.click()},N=()=>{var b;(b=p.current)==null||b.click()},I=b=>{var O;const S=(O=b.target.files)==null?void 0:O[0];if(S){const q=new FileReader;q.onload=X=>{var R;const E=(R=X.target)==null?void 0:R.result;a(E),s.setValue("logo",E)},q.readAsDataURL(S)}},k=b=>{var O;const S=(O=b.target.files)==null?void 0:O[0];if(S){const q=new FileReader;q.onload=X=>{var R;const E=(R=X.target)==null?void 0:R.result;o(E),s.setValue("background",E)},q.readAsDataURL(S)}},v=b=>{var O;const S=(O=b.target.files)==null?void 0:O[0];if(S){const q=new FileReader;q.onload=X=>{var R;const E=(R=X.target)==null?void 0:R.result;x(E),s.setValue("secondary_screen_image",E)},q.readAsDataURL(S)}};return y.useEffect(()=>{const b=s.getValues("logo"),S=s.getValues("background"),O=s.getValues("secondary_screen_image");b&&b!==t&&a(b),S&&S!==h&&o(S),O&&O!==m&&x(O)},[s.getValues("logo"),s.getValues("background"),s.getValues("secondary_screen_image")]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Xây dựng thương hiệu"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Thông tin thương hiệu của bạn áp dụng cho giao diện của hóa đơn, đặt hẹn và tiếp thị."})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("h3",{className:"text-base font-medium",children:"Logo"})}),e.jsx("div",{className:"flex items-start gap-4",children:e.jsx(c,{control:s.control,name:"logo",render:({field:b})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"flex h-32 w-32 cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100",onClick:f,children:t?e.jsx("img",{src:t,alt:"Logo preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx(te,{className:"h-8 w-8 text-gray-400"}),e.jsx(ae,{className:"mt-1 h-4 w-4 text-blue-500"})]})}),e.jsx(T,{type:"button",variant:"outline",size:"sm",className:"mt-2",onClick:f,disabled:n,children:"Tải lên"})]})}),e.jsx(d,{})]})})}),e.jsx("p",{className:"text-xs text-gray-500",children:"Kích thước tối thiểu 300 x 300 (px)"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium",children:"Hình nền trên thiết bị bán hàng"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Hình ảnh này xuất hiện trên màn hình 2 của các thiết bị pos 2 màn hình. Kích thước đề xuất 1920x1080 px"})]}),e.jsx(c,{control:s.control,name:"background",render:({field:b})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx("div",{className:"flex h-48 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100",onClick:j,children:h?e.jsx("img",{src:h,alt:"Background preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx("div",{className:"rounded-full border-2 border-gray-300 p-4",children:e.jsx(te,{className:"h-8 w-8 text-gray-400"})}),e.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"Tải lên một hình ảnh hỗ sơ"})]})})}),e.jsx(d,{})]})}),e.jsx("div",{className:"flex justify-start",children:e.jsxs(T,{type:"button",variant:"outline",size:"sm",onClick:j,disabled:n,className:"flex items-center gap-2",children:[e.jsx(ae,{className:"h-4 w-4"}),"Tải lên"]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium",children:"Hình nền trên màn hình 2"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Hình ảnh này xuất hiện trên màn hình 2 tại bước chốt bill. Kích thước đề xuất 1280x1080 px"})]}),e.jsx(c,{control:s.control,name:"secondary_screen_image",render:({field:b})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx("div",{className:"flex h-48 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100",onClick:N,children:m?e.jsx("img",{src:m,alt:"Background Screen 2 preview",className:"h-full w-full rounded-lg object-cover"}):e.jsxs("div",{className:"flex flex-col items-center justify-center",children:[e.jsx("div",{className:"rounded-lg border-2 border-gray-300 p-8",children:e.jsx(te,{className:"h-8 w-8 text-gray-400"})}),e.jsx(ae,{className:"mt-2 h-6 w-6 rounded-full bg-blue-500 p-1 text-white"})]})})}),e.jsx(d,{})]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium",children:"Video màn hình 2"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Vui lòng sử dụng đường dẫn cho phép tải video trực tiếp (Direct Link). Dung lượng video khuyến nghị không quá 100MB. Bạn có thể tham khảo hướng dẫn lấy Direct Link từ Google Drive tại mục dưới đây"})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(_,{className:"text-sm font-medium text-gray-700",children:"Video url"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(B,{className:"max-w-md",children:e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsx("p",{className:"font-medium",children:"Hướng dẫn lấy link direct cho video màn hình 2:"}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Bước 1:"})," Đăng nhập vào tài khoản Google Drive và tải lên video của bạn"," ",e.jsx("a",{href:"https://drive.google.com/drive/my-drive",target:"_blank",rel:"noopener noreferrer",className:"cursor-pointer text-blue-400 underline hover:text-blue-600",children:"Tại đây"})]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Bước 2:"})," Thiết lập chia sẻ video hiển thị với mọi người (General access: Anyone with the link). Sau đó copy link"]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Bước 3:"})," Truy cập trang tạo Direct Link của Google Drive"," ",e.jsx("a",{href:"https://sites.google.com/site/gdocs2direct/",target:"_blank",rel:"noopener noreferrer",className:"cursor-pointer text-blue-400 underline hover:text-blue-600",children:"Tại đây"}),". Sau đó nhập link vừa bạn copy. Bấm Create Direct Link. Bạn sẽ có Output link sử dụng tại FABi"]})]})]})})]})})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"secondary_screen_video",render:({field:b})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"Điền link video cho màn hình 2",disabled:n,...b})}),e.jsx(d,{})]})})})]})]})]}),e.jsx("input",{ref:l,type:"file",accept:"image/*",onChange:I,className:"hidden"}),e.jsx("input",{ref:u,type:"file",accept:"image/*",onChange:k,className:"hidden"}),e.jsx("input",{ref:p,type:"file",accept:"image/*",onChange:v,className:"hidden"})]})}function Os({form:s,isLoading:n=!1}){const[t,a]=y.useState(null),{data:h=[],isLoading:o}=Ze(),m=De(),x=s.watch("bank_id"),l=s.watch("bank_acc"),u=s.watch("bank_acc_name"),p=h.map(k=>({value:k.id.toString(),label:`${k.name} (${k.shortName})`})),f=x&&h.length>0&&h.find(k=>k.id.toString()===x)||null,j=ts.map(k=>({value:k.value,label:k.label})),N=!!(f&&(l!=null&&l.trim())&&(u!=null&&u.trim())),I=async()=>{var k;if(!(!N||!f||!l||!u))try{const v=await m.mutateAsync({bank_id:f.id.toString(),bank_acc:l.trim(),bank_acc_name:u.trim()});(k=v.data)!=null&&k.imageURL&&a(v.data.imageURL)}catch{}};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình VietQr"}),e.jsxs("div",{className:"flex gap-6",children:[e.jsxs("div",{className:"flex-[9] space-y-4",children:[e.jsx(c,{control:s.control,name:"bank_id",render:({field:k})=>{var v;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Danh sách ngân hàng"})}),e.jsx(r,{children:e.jsx(V,{options:p,value:((v=k.value)==null?void 0:v.toString())||"",onValueChange:k.onChange,placeholder:"Chọn ngân hàng",searchPlaceholder:"Tìm kiếm ngân hàng...",emptyText:"Không tìm thấy ngân hàng",disabled:n||o,className:"flex-1"})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"bank_acc",render:({field:k})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Số tài khoản"})}),e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập số tài khoản",disabled:n,className:"flex-1",...k})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"bank_acc_name",render:({field:k})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tên tài khoản"})}),e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập tên tài khoản",disabled:n,className:"flex-1",...k})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"print_qrcode_pay",render:({field:k})=>{var v;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"In mã Qrcode phục vụ thanh toán"})}),e.jsx(r,{children:e.jsx(V,{options:j,value:((v=k.value)==null?void 0:v.toString())||"0",onValueChange:k.onChange,placeholder:"Chọn cấu hình in QR",searchPlaceholder:"Tìm kiếm cấu hình...",emptyText:"Không tìm thấy cấu hình",disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})}})]}),e.jsx("div",{className:"flex items-start justify-center",children:t?e.jsx("div",{className:"rounded-lg border p-2 shadow-sm",children:e.jsx("img",{src:t,alt:"VietQR Code",className:"mx-auto h-36 object-contain",onError:()=>a(null)})}):e.jsx(T,{type:"button",variant:"outline",onClick:I,disabled:n||!N||m.isPending,className:"border-blue-600 text-blue-600 hover:bg-blue-50",children:m.isPending?"Đang tạo...":"Lấy mã Qr"})})]})]})}function Vs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình cho MoMo QR Đa Năng"}),e.jsx(c,{control:s.control,name:"auto_check_momo_aio",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(_,{className:"w-[200px] pt-2",children:"Tự động kiểm tra thanh toán từ MoMo QR Đa Năng"}),e.jsx(r,{children:e.jsx(D,{onValueChange:a=>t.onChange(Number(a)),value:t.value.toString(),className:"flex-1 space-y-4",disabled:n,children:as.map(a=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(z,{value:a.value.toString(),id:a.value.toString(),className:"mt-1"}),e.jsx(U,{htmlFor:a.value.toString(),className:"cursor-pointer text-sm leading-relaxed",children:a.label})]},a.value))})})]}),e.jsx(d,{})]})})]})}function Ps({form:s,isLoading:n=!1}){const t=s.watch("print_type"),a=t==="PROVISIONAL_INVOICE"||t==="CHECK_LIST_AND_PROVISIONAL_INVOICE";return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình in tạm tính, chốt đồ"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"print_type",render:({field:h})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(_,{className:"w-[200px] pt-2",children:"Kiểu in"}),e.jsx(r,{children:e.jsx(D,{value:h.value,onValueChange:h.onChange,className:"grid flex-1 grid-cols-2 gap-4",disabled:n,children:ls.map(o=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(z,{value:o.value,id:o.value}),e.jsx(U,{htmlFor:o.value,className:"cursor-pointer text-sm",children:o.label})]},o.value))})})]}),e.jsx(d,{})]})}),a&&e.jsx(c,{control:s.control,name:"print_limit",render:({field:h})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[200px]",children:"Giới hạn số lần in tạm tính"}),e.jsx(r,{children:e.jsx(w,{placeholder:"Không giới hạn",disabled:n,className:"flex-1",...h})})]}),e.jsx(d,{})]})})]})]})}function me(s){if(!s||!Array.isArray(s))return[];if(s.length===0)return[];const n=s[0];if(typeof n=="string")return s;if(typeof n=="object"&&n!==null){if("source_id"in n)return s.map(t=>t.source_id);if("id"in n)return s.map(t=>t.id)}return[]}function Z({open:s,onOpenChange:n,onSourcesChange:t,appliedSources:a}){const{sources:h}=Ce({skipLimit:!0}),[o,m]=y.useState(""),[x,l]=y.useState([]),[u,p]=y.useState(!1),[f,j]=y.useState(!1);y.useEffect(()=>{const C=me(a);l(C)},[a,s]);const N=y.useMemo(()=>h?h.filter(C=>C.source_name.toLowerCase().includes(o.toLowerCase())):[],[h,o]),I=y.useMemo(()=>N.length?N.filter(C=>x.includes(C.source_id)):[],[N,x]),k=y.useMemo(()=>N.length?N.filter(C=>!x.includes(C.source_id)):[],[N,x]),v=C=>{l(A=>A.includes(C)?A.filter(G=>G!==C):[...A,C])},b=I.length,S=N.length,O=S>0&&b===S,q=b>0&&b<S,X=()=>{if(O){const C=N.map(A=>A.source_id);l(A=>A.filter(G=>!C.includes(G)))}else{const C=N.map(A=>A.source_id);l(A=>{const G=[...A];return C.forEach(oe=>{G.includes(oe)||G.push(oe)}),G})}},E=()=>{t(x),n(!1)},R=()=>{const C=me(a);l(C),n(!1)};return e.jsx(He,{title:"Chọn nguồn đơn",open:s,onOpenChange:n,onCancel:R,onConfirm:E,cancelText:"Hủy",confirmText:"Xong",maxWidth:"sm:max-w-md",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(w,{placeholder:"Tìm kiếm",value:o,onChange:C=>m(C.target.value),className:"w-full"}),I.length>0&&e.jsxs("div",{className:"rounded-lg bg-green-50 p-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(g,{id:"select-all",checked:O,...q&&{"data-indeterminate":"true"},onCheckedChange:X,className:"data-[state=checked]:border-green-600 data-[state=checked]:bg-green-600"}),e.jsxs("label",{htmlFor:"select-all",className:"cursor-pointer text-sm font-medium text-green-700",children:["Đã chọn ",b]}),e.jsx(T,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>p(!u),children:u?e.jsx(he,{className:"h-3 w-3"}):e.jsx(J,{className:"h-3 w-3"})})]}),!u&&e.jsx("div",{className:"mt-3 space-y-2",children:I.map(C=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(g,{id:`selected-${C.source_id}`,checked:x.includes(C.source_id),onCheckedChange:()=>v(C.source_id)}),e.jsx("label",{htmlFor:`selected-${C.source_id}`,className:"flex-1 cursor-pointer text-sm",children:C.source_name})]},C.source_id))})]}),k.length>0&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-700",children:["Còn lại ",k.length]}),e.jsx(T,{variant:"ghost",size:"sm",className:"ml-auto h-6 px-2 text-xs",onClick:()=>j(!f),children:f?e.jsx(he,{className:"h-3 w-3"}):e.jsx(J,{className:"h-3 w-3"})})]}),!f&&e.jsx("div",{className:"mt-3 max-h-80 space-y-2 overflow-y-auto",children:k.map(C=>e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(g,{id:C.source_id,checked:x.includes(C.source_id),onCheckedChange:()=>v(C.source_id)}),e.jsx("label",{htmlFor:C.source_id,className:"flex-1 cursor-pointer text-sm",children:C.source_name})]},C.source_id))})]})]})})}function As({form:s,isLoading:n=!1,storeUid:t}){const[a,h]=y.useState(!1),{sources:o}=Ce({storeUid:t,skipLimit:!0}),m=(o==null?void 0:o.length)||0,x=u=>{s.setValue("source_ids_selected",u)},l=()=>{h(!0)};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Áp dụng nguồn đơn tại cửa hàng"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Mặc định áp dụng nguồn đơn Tại chỗ và Mang về"})]}),e.jsx(c,{control:s.control,name:"sources_print",render:({field:u})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[200px]",children:"Áp dụng nguồn đơn"}),e.jsx(r,{children:e.jsxs(T,{type:"button",variant:"outline",disabled:n,onClick:l,className:"border-blue-600 text-blue-600 hover:bg-blue-50",children:[m," nguồn được áp dụng"]})})]}),e.jsx(d,{})]})}),e.jsx(Z,{open:a,onOpenChange:h,onSourcesChange:x,appliedSources:o})]})}function Ms({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"mb-2 flex items-center gap-2",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Cấu hình Ahamove"})}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"Kết nối Ahamove và cài đặt cấu hình cho cửa hàng"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"is_ahamove_active",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(_,{className:"w-[220px]",children:"Kích hoạt Ahamove"}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:a=>{t.onChange(a),a&&P.success("Kết nối Ahamove tại Ứng dụng để kích hoạt cấu hình")},disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"phone_manager",render:({field:t})=>{const a=s.watch("is_ahamove_active");return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs(_,{className:"w-[270px]",children:["Số điện thoại liên hệ ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập số điện thoại cho tài xế liên hệ",disabled:n||!a,...t})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"ahamove_payment_method",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[200px]",children:"Phương thức thanh toán"}),e.jsx(r,{children:e.jsx("div",{className:"flex gap-6",children:cs.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(g,{id:a.value,checked:typeof t.value=="string"&&t.value.includes(a.value),onCheckedChange:h=>{const o=typeof t.value=="string"?t.value.split(",").filter(Boolean):[],m=a.value;if(h){const x=[...o,m];t.onChange(x.join(","))}else{const x=o.filter(l=>l!==m);t.onChange(x.join(","))}},disabled:n}),e.jsx(U,{htmlFor:a.value,children:a.label})]},a.value))})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"ahamove_voucher_default",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[270px]",children:"Ahamove voucher"}),e.jsx(r,{children:e.jsx(w,{placeholder:"Nhập Ahamove voucher (nếu có)",disabled:n,...t})})]}),e.jsx(d,{})]})})]})]})}function qs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Mô hình hoạt động"}),e.jsx("div",{className:"space-y-6",children:e.jsx(c,{control:s.control,name:"operate_model",render:({field:t})=>{var a;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx(_,{className:"w-[200px] pt-2",children:"Mô hình hoạt động"}),e.jsx(r,{children:e.jsxs(D,{value:((a=t.value)==null?void 0:a.toString())||"0",onValueChange:t.onChange,disabled:n,className:"flex-1 space-y-4",children:[e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(z,{value:"0",id:"allow_cashier_remove"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:"allow_cashier_remove",className:"text-base font-medium",children:"Cho phép thu ngân bỏ món"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Mô hình cho phép thu ngân bỏ món ngay cả sau khi in tạm tính"})]})]}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(z,{value:"1",id:"limit_cashier_remove"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:"limit_cashier_remove",className:"text-base leading-relaxed font-medium",children:"Giới hạn quyền bỏ món của thu ngân"}),e.jsx("p",{className:"text-sm leading-relaxed text-gray-600",children:"Mô hình cho phép thu ngân bỏ món nhưng không được phép bỏ những món đã in tạm tính. Thu ngân cũng không được phép thêm voucher sau khi đã in tạm tính"})]})]}),e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(z,{value:"2",id:"no_cashier_remove"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(U,{htmlFor:"no_cashier_remove",className:"text-base leading-relaxed font-medium",children:"Không cho phép thu ngân bỏ món"}),e.jsx("p",{className:"text-sm leading-relaxed text-gray-600",children:"Mô hình không cho phép thu ngân bỏ món, các yêu cầu bỏ món cần được sự đồng ý của Quản lý"})]})]})]})})]}),e.jsx(d,{})]})}})})]})}function Es({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình Voucher và Hội viên"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"exchange_points_for_voucher",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Đổi điểm lấy voucher"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"view_voucher_of_member",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Xem voucher của thành viên"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_checkin_by_phone_number",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Không checkin bằng SĐT"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"multi_voucher",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Cho phép sử dụng nhiều mã giảm giá trong 1 đơn hàng"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"find_member",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"leading-relaxed",children:"Tìm kiếm hội viên CRM theo tên hoặc số điện thoại"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-6 w-6 cursor-help text-gray-400 hover:text-gray-600"})}),e.jsx(B,{children:e.jsx("p",{className:"max-w-xs",children:'Nếu muốn tìm kiếm theo tên bạn vào "Thiết lập nhà hàng" chọn cấu hình Mã check-in gồm cả chữ và số'})})]})})]}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Rs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình nhà hàng buffet"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"is_run_buffet",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Nhà hàng buffet"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_buffet_item",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Bắt buộc phải chọn vé buffet"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Ks({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Quản lý ca"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"enable_count_money",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Đếm tờ tiền khi chốt ca"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"disable_shift_total_amount",render:({field:t})=>{var a;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Cấu hình thu ngân và doanh thu trong ca"})}),e.jsx(r,{children:e.jsx(V,{value:((a=t.value)==null?void 0:a.toString())||"0",onValueChange:t.onChange,disabled:n,placeholder:"Chọn cấu hình thu ngân",className:"flex-1",options:[{value:"0",label:"Cho phép thu ngân thấy doanh thu trong ca"},{value:"1",label:"Thu ngân không được thấy doanh thu trong ca, nhưng vẫn thấy danh sách đơn hàng"},{value:"2",label:"Thu ngân không được thấy doanh thu trong ca và danh sách đơn hàng"}]})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"allow_remove_shift_open",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{className:"w-[240px] leading-relaxed",children:"Cho phép xóa hóa đơn trên CMS khi ca đang mở"}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"close_shift_auto_logout",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tự động đăng xuất khi chốt ca"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_close_shift_in_day",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Yêu cầu phải đóng ca trong ngày"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Fs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Invoice"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"discount_reverse_on_price",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Ẩn tiền Chiết khấu trên HĐDT"})}),e.jsx(r,{children:e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n}),e.jsx("span",{className:"text-sm text-gray-600",children:"Giảm giá khấu trừ vào giá món"})]})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"inv_skip_item_no_price",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Ẩn món 0đ trên HĐDT"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"auto_export_vat",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Cho phép auto xuất VAT khi chốt ca"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"export_time_vat",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"leading-relaxed",children:"Cấu hình thời gian xuất VAT ở POS (phút)"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-6 w-6 cursor-help text-gray-400 hover:text-gray-600"})}),e.jsx(B,{children:e.jsx("p",{className:"max-w-xs",children:"Cấu hình thời gian cho phép xuất VAT của 1 hóa đơn tính từ lúc in thanh toán tại POS"})})]})})]}),e.jsx(r,{children:e.jsx(w,{placeholder:"Không giới hạn thời gian",disabled:n,className:"flex-1",...t})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_vat_info",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Bắt buộc nhập thông tin VAT khi thanh toán"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"pm_export_vat",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Mặc định phương thức thanh toán TM/CK"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"bill_auto_export_vat",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Auto xuất vat khi in hoá đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"sorted_by_print",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"leading-relaxed",children:"Sắp xếp món theo thứ tự trên hoá đơn"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-6 w-6 cursor-help text-gray-400 hover:text-gray-600"})}),e.jsx(B,{children:e.jsx("p",{className:"max-w-xs",children:"Chỉ áp dụng đối với hoá đơn xuất lẻ"})})]})})]}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Bs({form:s,isLoading:n=!1}){const{data:t=[],isLoading:a}=Qe(),h=t.map(o=>({value:o.id,label:o.role_name}));return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Cấu hình nâng cao POS"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{control:s.control,name:"enable_cash_drawer",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Hiển thị nút mở két"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"confirm_request",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Hiển thị nút xác nhận yêu cầu"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"use_order_control",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sử dụng KDS Order Control"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_tab_delivery",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Hiển thị tab quản lý giao hàng"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_note_delete_item",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Yêu cầu chọn lý do bỏ món"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"service_charge_optional",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cho phép tự nhập phí dịch vụ"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_peo_count",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Yêu cầu nhập số khách khi vào bàn"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_confirm_merge_table",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Yêu cầu xác nhận khi gộp đơn chuyển bàn"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"hide_peo_count",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Ẩn thông tin số khách"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_edit_item_price_while_selling",render:({field:o})=>{var m;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Cấu hình sửa giá, chiết khấu trực tiếp khi bán"})}),e.jsx(r,{children:e.jsx(V,{value:((m=o.value)==null?void 0:m.toString())||"0",onValueChange:o.onChange,disabled:n,placeholder:"Chọn cấu hình",className:"flex-1",options:[{value:"0",label:"Không cho phép thay đổi"},{value:"1",label:"Cho phép sửa giá khi bán hàng"},{value:"2",label:"Cho phép thu ngân chiết khấu trực tiếp khi bán hàng"},{value:"3",label:"Cho phép sửa giá khi bán hàng và thu ngân được phép chiết khấu trực tiếp khi bán hàng"}]})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"role_quick_login",render:({field:o})=>{var m;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Quyền nhân viên được khởi tạo khi quét mã QR từ POS"})}),e.jsx(r,{children:e.jsx(V,{value:(m=o.value)==null?void 0:m.toString(),onValueChange:o.onChange,disabled:n||a,placeholder:"Chọn quyền nhân viên",className:"flex-1",options:h})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"auto_confirm_o2o_post_paid",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Tự động xác nhận với đơn O2O trả sau"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"resetItemOutOfStockStatus",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Trạng thái hết món không tự động làm mới hàng ngày"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"resetItemQuantityNewDay",render:({field:o})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"leading-relaxed font-medium",children:"Không tự động đặt lại số lượng món qua ngày"})}),e.jsx(r,{children:e.jsx(g,{checked:o.value,onCheckedChange:o.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Hs({form:s,isLoading:n=!1}){const[t]=y.useState(6),a=()=>{const l=Math.pow(10,t-1),u=Math.pow(10,t)-1;return(Math.floor(Math.random()*(u-l+1))+l).toString()},h=[{value:"0",label:"Không tự động thay đổi mã Pin khi sử dụng"},{value:"0.02",label:"Mã Pin chỉ sử dụng được một lần"},{value:"1",label:"Sau 1 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"2",label:"Sau 2 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"3",label:"Sau 3 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"4",label:"Sau 4 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"5",label:"Sau 5 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"6",label:"Sau 6 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"7",label:"Sau 7 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"8",label:"Sau 8 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"9",label:"Sau 9 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"},{value:"10",label:"Sau 10 phút sử dụng, hệ thống sẽ tự động đổi mã PIN"}],o=()=>{const l=a();s.setValue("pin_code",l)},m=l=>{const u=l.replace(/\D/g,"").slice(0,t);s.setValue("pin_code",u)},x=s.watch("pin_code")||"";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Mã PIN"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Mã được tạo tự động và được sử dụng để quản lý xác nhận cho phép thực hiện các chức năng của quản lý mà không cần phải thay đổi tài khoản!"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"pin_code",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsxs(_,{className:"font-medium",children:["Mã PIN ",e.jsx("span",{className:"text-red-500",children:"*"})]})}),e.jsxs("div",{className:"flex flex-1 items-center gap-2",children:[e.jsx(r,{children:e.jsxs("div",{className:"relative flex-1",children:[e.jsx(w,{...l,value:x,onChange:u=>m(u.target.value),placeholder:"Nhập mã PIN",disabled:n,className:"pr-16",maxLength:t}),e.jsxs("div",{className:"absolute top-1/2 right-3 -translate-y-1/2 text-xs text-gray-400",children:[x.length,"/",t]})]})}),e.jsx(T,{type:"button",variant:"outline",size:"sm",onClick:o,disabled:n,className:"shrink-0",children:e.jsx(We,{className:"h-4 w-4"})})]})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"time_out_use_pin",render:({field:l})=>{var u;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tự động thay đổi mã PIN"})}),e.jsx(r,{children:e.jsx(V,{value:((u=l.value)==null?void 0:u.toString())||"0",onValueChange:l.onChange,disabled:n,placeholder:"Chọn cấu hình tự động thay đổi",className:"flex-1",options:h})})]}),e.jsx(d,{})]})}})]})]})}function Qs({form:s,isLoading:n=!1}){const t=s.watch("open_at"),a=x=>{const l=new Date,u=new Date(l);u.setDate(l.getDate()+1);const p=k=>k.toLocaleDateString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric"}),f=k=>k.toString().padStart(2,"0")+":00",j=f(x),N=x===0?23:x-1,I=f(N)==="00:00"?"23:59":f(N)+":59";return x===0?`${p(l)} 00:00 - ${p(l)} 23:59`:`${p(l)} ${j} - ${p(u)} ${I}`},h=t?parseInt(t.toString(),10):0,m=!isNaN(h)&&h>=0&&h<=23?a(h):"04/08/2025 00:00 - 04/08/2025 23:59";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Giờ bán hàng"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"Chi cái đặt khi cửa hàng của bạn bán qua 12h đêm"}),e.jsxs("p",{children:["Các báo cáo có chú thích: Báo cáo được tính từ ",m," . Đảm bảo các hóa đơn qua đêm sẽ được tổng hợp lại theo 1 ngày bán hàng."]})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsx(c,{control:s.control,name:"open_at",render:({field:x})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Giờ bắt đầu bán"})}),e.jsx(r,{children:e.jsx(w,{...x,type:"number",min:"0",max:"23",placeholder:"0",disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})})})]})}function Us({form:s,isLoading:n=!1}){const t=[{value:0,label:"Không theo dõi"},{value:1,label:"Theo dõi dữ liệu đơn hàng"},{value:2,label:"Theo dõi dữ liệu đơn hàng và thông tin món"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Theo dõi bán hàng dưới POS"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"Tính năng chỉ áp dụng cho mô hình bán dine-in."}),e.jsx("p",{children:"Các trạng thái, báo cáo bán hàng dưới POS (số bàn, số hóa đơn) sẽ được ghi lại định kỳ từ 5 - 10 phút."})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsx(c,{control:s.control,name:"tracking_sale",render:({field:a})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Theo dõi thông tin tại nhà hàng"})}),e.jsx(r,{children:e.jsx(V,{value:a.value,onValueChange:a.onChange,disabled:n,placeholder:"Chọn cấu hình theo dõi",className:"flex-1",options:t})})]}),e.jsx(d,{})]})})})]})}function Xs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Báo cáo lượt order"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"Báo cáo được ghi nhận từ thời điểm được kích hoạt."}),e.jsx("p",{children:"Bạn cần đăng nhập lại CMS để sử dụng đầy đủ tính năng sau kích hoạt."})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"enable_turn_order_report",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Kích hoạt báo cáo lượt order"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"change_log_detail",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tìm kiếm chi tiết món"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Gs({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Cấu hình sửa thực đơn tại cửa hàng"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Thay đổi chỉ áp dụng tại cửa hàng, không ảnh hưởng tới thực đơn của thương hiệu"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"enable_change_item_in_store",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sửa thực đơn tại cửa hàng"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_change_item_type_in_store",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sửa nhóm món tại cửa hàng"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_change_printer_position_in_store",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sửa vị trí máy in tại cửa hàng"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"prevent_create_custom_item",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Không được tạo món tùy chọn"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_custom_item_vat",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Yêu cầu món tùy chọn phải nhập VAT"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_category_for_custom_item",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Yêu cầu món tùy chọn phải chọn nhóm món"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"is_menu_by_source",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"font-medium",children:"Hiển thị menu theo nguồn"}),e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 text-gray-400 hover:text-gray-600"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{children:"Nếu món ăn có khai báo giá theo nguồn sẽ chỉ hiển thị với nguồn đó, món ăn không khai báo theo nguồn sẽ luôn hiển thị. Trường hợp bàn hoặc hoá đơn không có nguồn sẽ hiển thị tất."})})]})]}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Ys({form:s,isLoading:n=!1}){const[t,a]=y.useState(!1),h=s.watch("sources_not_print"),o=s.watch("enable_tran_no_prefix"),m=()=>{a(!0)},x=p=>{s.setValue("sources_not_print",p)},l=[{value:"0",label:"Sinh số hoá đơn ngẫu nhiên"},{value:"1",label:"Sinh số hoá đơn theo thứ tự khi in order"},{value:"2",label:"Sinh số hoá đơn theo thứ tự khi in bill"}],u=[{value:"DAILY",label:"Đặt lại số hoá đơn theo ngày"},{value:"MONTHLY",label:"Đặt lại số hoá đơn theo tháng"},{value:"YEARLY",label:"Đặt lại số hoá đơn theo năm"},{value:"NO_RESET",label:"Không đặt lại"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Cấu hình số hóa đơn"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"tran_no_syn_order",render:({field:p})=>{var f;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sinh số hóa đơn theo thứ tự"})}),e.jsx(r,{children:e.jsx(V,{value:((f=p.value)==null?void 0:f.toString())||"0",onValueChange:p.onChange,disabled:n,placeholder:"Chọn cách sinh số hóa đơn",className:"flex-1",options:l})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"enable_tran_no_prefix",render:({field:p})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sử dụng tiền tố cho số hóa đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:p.value,onCheckedChange:p.onChange,disabled:n})})]}),e.jsx(d,{})]})}),o&&e.jsx(c,{control:s.control,name:"tran_no_prefix",render:({field:p})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"font-medium",children:"Tiền tố của số hóa đơn"}),e.jsx($,{children:e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 cursor-help text-gray-400"})}),e.jsx(B,{className:"max-w-md p-4",children:e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsx("p",{children:"Ký tự này sẽ được chèn vào trước mỗi mã số hóa đơn."}),e.jsx("p",{className:"font-medium",children:"Một số định dạng có thể hữu ích với bạn:"}),e.jsxs("ul",{className:"space-y-1 text-xs",children:[e.jsxs("li",{children:[e.jsx("strong",{children:"TIME"})," hoặc ",e.jsx("strong",{children:"TIME_XXX"}),": Lấy ngày hiện tại làm tiền tố."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"TFYYMMdd"})," hoặc ",e.jsx("strong",{children:"TFYYMMdd_XXX"}),": Lấy thời gian theo format YYMMdd làm tiền tố. Khi sử dụng định dạng này, mã số hóa đơn của bạn sẽ là: 250101001."]}),e.jsxs("li",{children:[e.jsx("strong",{children:"NOPRE"})," hoặc ",e.jsx("strong",{children:"NOPRE_XXX"}),": Không sử dụng tiền tố ở số hóa đơn."]})]}),e.jsxs("p",{className:"text-xs text-gray-600",children:[e.jsx("strong",{children:"*Chú thích:"}),e.jsx("br",{}),"_XXX: dành để giới hạn số ký tự trong dãy số hóa đơn. Định dạng hợp lệ: _X; _XX; _XXX; _XXXX. Nếu không sử dụng tiền tố này thì mặc định số hóa đơn có 4 ký tự."]})]})})]})})]}),e.jsx(r,{children:e.jsx(w,{...p,value:p.value||"",placeholder:"Nhập tiền tố cho số hóa đơn",disabled:n,className:"flex-1",maxLength:30})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"reset_tran_no_period",render:({field:p})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Thời gian đặt lại số hóa đơn"})}),e.jsx(r,{children:e.jsx(V,{value:p.value||"never",onValueChange:p.onChange,disabled:n,placeholder:"Chọn thời gian đặt lại",className:"flex-1",options:u})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"sources_not_print",render:({field:p})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cấu hình nguồn không in hóa đơn"})}),e.jsx(r,{children:e.jsxs(T,{type:"button",variant:"outline",disabled:n,onClick:m,className:"border-blue-600 text-blue-600 hover:bg-blue-50",children:[h==null?void 0:h.length," nguồn được áp dụng"]})})]}),e.jsx(d,{})]})})]}),e.jsx(Z,{open:t,onOpenChange:a,onSourcesChange:x,appliedSources:h})]})}function zs({form:s,isLoading:n=!1}){const[t,a]=y.useState(!1),[h,o]=y.useState(!1),[m,x]=y.useState([]),[l,u]=y.useState([]),p=s.watch("sources_label_print"),f=s.watch("sources_print");y.useEffect(()=>{Array.isArray(p)?x(p):x([])},[p]),y.useEffect(()=>{Array.isArray(f)?u(f):u([])},[f]);const j=()=>{a(!0)},N=()=>{o(!0)},I=v=>{x(v),s.setValue("sources_label_print",v)},k=v=>{u(v),s.setValue("sources_print",v)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Quản lý in tem nhãn và yêu cầu (order)"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"print_order_at_checkout",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"In phiếu yêu cầu khi thanh toán"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"print_label_at_checkout",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"In tem nhãn khi thanh toán"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"sources_label_print",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cấu hình in tem nhãn theo nguồn"})}),e.jsx(r,{children:e.jsxs(T,{type:"button",variant:"outline",disabled:n,onClick:j,className:"flex-1 justify-start border-blue-600 text-blue-600 hover:bg-blue-50",children:[m.length," nguồn được áp dụng"]})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"sources_print",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cấu hình in yêu cầu (order) theo nguồn"})}),e.jsx(r,{children:e.jsxs(T,{type:"button",variant:"outline",disabled:n,onClick:N,className:"flex-1 justify-start border-blue-600 text-blue-600 hover:bg-blue-50",children:[l.length," nguồn được áp dụng"]})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"print_bill_order_area",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"font-medium",children:"Cho phép sử dụng máy in phiếu yêu cầu để in hóa đơn theo vị trí/khu vực cho PDA"}),e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 flex-shrink-0 text-gray-400 hover:text-gray-600"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{children:"Các máy in được gán vị trí tại của hàng được cấu hình theo khu vực thỏa mãn điều kiện sẽ được in ra."})})]})]}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"split_combo",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tách các món trong combo khi in"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"ignore_combo_note",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Bỏ note combo với các món bị tách"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"show_item_price_zero",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"font-medium",children:"Không thay thế món cha 0đ"}),e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 flex-shrink-0 text-gray-400 hover:text-gray-600"})}),e.jsx(B,{className:"max-w-xs",children:e.jsx("p",{children:"Nếu món cha 0đ thì món topping đầu tiên (không có customization) sẽ thay thế vị trí món cha"})})]})]}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"enable_delete_order_bill",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Sử dụng phiếu hủy đổ"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"print_item_switch_table",render:({field:v})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Phiếu chuyển bàn in thêm danh sách món"})}),e.jsx(r,{children:e.jsx(g,{checked:v.value,onCheckedChange:v.onChange,disabled:n})})]}),e.jsx(d,{})]})})]}),e.jsx(Z,{open:t,onOpenChange:a,onSourcesChange:I,appliedSources:m}),e.jsx(Z,{open:h,onOpenChange:o,onSourcesChange:k,appliedSources:l})]})}function $s({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-lg font-semibold",children:"Cấu hình thiết bị Self Order"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"allway_show_tag_so",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Hiển thị nhập thẻ bán với mọi phương thức thanh toán"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"use_shift_pos",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cho phép S.O sử dụng ca hiện tại của thu ngân"})}),e.jsx(r,{children:e.jsx(g,{checked:t.value,onCheckedChange:t.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function Ls({form:s,isLoading:n=!1}){return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-lg font-semibold",children:"Cấu hình trung tâm thương mại"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"counter_code",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex w-[240px] items-center gap-2",children:[e.jsx(_,{className:"font-medium",children:"Mã định danh của hàng"}),e.jsxs(K,{children:[e.jsx(F,{asChild:!0,children:e.jsx(H,{className:"h-4 w-4 text-gray-400"})}),e.jsx(B,{children:e.jsx("p",{children:"Mã định danh duy nhất của cửa hàng trong hệ thống trung tâm thương mại"})})]})]}),e.jsx(r,{children:e.jsx(w,{...t,disabled:n,placeholder:"Nhập mã định danh của hàng",className:"flex-1"})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"counter_mails",render:({field:t})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Email nhận file báo cáo"})}),e.jsx(r,{children:e.jsx(w,{value:Array.isArray(t.value)?t.value.join(", "):"",onChange:a=>{const o=a.target.value.split(",").map(m=>m.trim()).filter(Boolean);t.onChange(o)},disabled:n,placeholder:"Nhập nhiều email, phân tách bằng dấu phẩy",type:"text",className:"flex-1"})})]}),e.jsx(d,{})]})})]})]})}const Ws=[{value:"0",label:"Mặc định"},{value:"1",label:"Mẫu hóa đơn 1"},{value:"2",label:"Mẫu hóa đơn 2"},{value:"3",label:"Mẫu hóa đơn 3"},{value:"4",label:"Mẫu hóa đơn 4"},{value:"5",label:"Mẫu tùy chỉnh"}],Js=[{value:"0",label:"Không tách món combo"},{value:"1",label:"Tách món trong combo và in"},{value:"2",label:"Tách món trong combo và không in"}],Zs=[{value:0,label:"Mặc định"},{value:1,label:"Xác nhận và in hóa đơn"},{value:2,label:"Thanh toán và không in hóa đơn"}],Ds=[{value:0,label:"Mặc định"},{value:4,label:"Hiển thị đóng ca"},{value:8,label:"Hiển thị đóng ca và in phiếu đóng ca"}];function en({form:s,isLoading:n}){const[t,a]=y.useState(0),[h,o]=y.useState(0),m=s.watch("disable_print_button_in_payment_screen")||0;y.useEffect(()=>{const l=(m&1)===1?1:(m&2)===2?2:0,u=(m&4)===4?4:(m&8)===8?8:0;a(l),o(u)},[m]);const x=(l,u)=>{const p=(l|u)>>>0;s.setValue("disable_print_button_in_payment_screen",p,{shouldDirty:!0,shouldTouch:!0})};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsx("h2",{className:"mb-2 text-xl font-semibold",children:"Cấu hình in ấn"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx(c,{control:s.control,name:"group_item",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Gộp món khi in chốt đơn, tạm tính, in hóa đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"bill_template",render:({field:l})=>{var u;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Mẫu hóa đơn"})}),e.jsx(r,{children:e.jsx(V,{options:Ws,value:((u=l.value)==null?void 0:u.toString())||"0",onValueChange:p=>l.onChange(p===""?void 0:Number(p)),disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"prevent_cashier_edit_printer",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Không cho thu ngân sửa máy in"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"disable_print_button_in_payment_screen",render:()=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cấu hình màn thanh toán tại POS"})}),e.jsx(r,{children:e.jsx(V,{options:Zs,value:t,onValueChange:l=>{const u=l===""?0:Number(l);a(u),x(u,h)},disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"disable_print_button_in_payment_screen",render:()=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Cấu hình nút đóng ca tại POS"})}),e.jsx(r,{children:e.jsx(V,{options:Ds,value:h,onValueChange:l=>{const u=l===""?0:Number(l);o(u),x(t,u)},disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"is_show_logo_in_provisional_invoice",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Hiển thị logo trên phiếu tạm tính"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"report_item_combo_split",render:({field:l})=>{var u;return e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Tách món combo khi in chốt ca"})}),e.jsx(r,{children:e.jsx(V,{options:Js,value:((u=l.value)==null?void 0:u.toString())||"0",onValueChange:p=>l.onChange(p===""?void 0:p),disabled:n,className:"flex-1"})})]}),e.jsx(d,{})]})}}),e.jsx(c,{control:s.control,name:"tem_invoice_fake_bill",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Phiếu tạm tính in giống hóa đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"hideItemPriceAfterPrintBill",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Ẩn món có giá 0đ khi in hóa đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"hideItemPriceAfterPrintChecklist",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Ẩn món có giá 0đ khi in chốt đơn"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"require_pin_reprint",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"In lại hóa đơn yêu cầu nhập mã pin"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})}),e.jsx(c,{control:s.control,name:"prevent_print_order_transfer",render:({field:l})=>e.jsxs(i,{children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"flex w-[240px] items-center gap-2",children:e.jsx(_,{className:"font-medium",children:"Không in hóa đơn đối với các giao dịch thanh toán bằng MoMo hoặc ví điện tử có sự đồng xác nhận thanh toán"})}),e.jsx(r,{children:e.jsx(g,{checked:l.value,onCheckedChange:l.onChange,disabled:n})})]}),e.jsx(d,{})]})})]})]})}function sn({form:s,isLoading:n=!1,storeUid:t}){const[a,h]=y.useState(!1);return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{children:e.jsxs(T,{type:"button",variant:"ghost",onClick:()=>h(!a),className:"flex h-auto items-center gap-2 p-0 text-xl font-bold text-blue-600 hover:bg-transparent hover:text-blue-700",children:[e.jsx("span",{children:"Cấu hình nâng cao"}),a?e.jsx(xe,{className:"h-5 w-5"}):e.jsx(J,{className:"h-5 w-5"})]})}),a&&e.jsxs("div",{className:"space-y-6 pt-6",children:[e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(qs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Es,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Rs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ks,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Fs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Bs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Hs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Qs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Us,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Xs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Gs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ys,{form:s,isLoading:n,storeUid:t})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(zs,{form:s,isLoading:n,storeUid:t})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(en,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx($s,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ls,{form:s,isLoading:n})}),e.jsx("div",{className:"flex items-center justify-center",children:e.jsxs(T,{type:"button",variant:"ghost",onClick:()=>h(!a),className:"text-md flex h-auto items-center justify-center gap-2 p-0 font-bold text-blue-600 hover:bg-transparent hover:text-blue-700",children:[e.jsx("span",{children:"Ẩn bớt"}),a?e.jsx(xe,{className:"h-5 w-5"}):e.jsx(J,{className:"h-5 w-5"})]})})]})]})}function nn({form:s,isLoading:n=!1,storeUid:t}){const{data:a=[],isLoading:h}=Ue({storeUid:t,params:{skip_limit:!0},enabled:!0}),o=y.useMemo(()=>a.filter(x=>x.isActive).map(x=>({value:x.device_code||"",label:`${x.name}`})),[a]),m=()=>{console.log("Adding device...")};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h2",{className:"mb-6 text-xl font-semibold",children:"Thiết bị"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Một điểm bán hàng có thể có 1 hoặc nhiều thiết bị bán hàng."}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Mã quản lý thiết bị"}),e.jsx("div",{className:"flex h-4 w-4 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600",children:"?"})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"store_id",render:({field:x})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(w,{placeholder:"XP5446MG969X",disabled:!0,...x})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-center gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Không cho phép kích thiết bị ra khỏi bàn trừ máy chủ"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"no_kick_pda",render:({field:x})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx("div",{className:"flex items-center space-x-2",children:e.jsx(g,{id:"no_kick_pda",checked:x.value,onCheckedChange:x.onChange,disabled:n})})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Thiết bị nhận đơn online"}),e.jsx("div",{className:"flex h-4 w-4 items-center justify-center rounded-full bg-gray-200 text-xs text-gray-600",children:"?"})]})}),e.jsx("div",{className:"col-span-9",children:e.jsx(c,{control:s.control,name:"device_receive_online",render:({field:x})=>e.jsxs(i,{children:[e.jsx(r,{children:e.jsx(V,{options:o,value:x.value,onValueChange:x.onChange,placeholder:"Chọn thiết bị nhận đơn online tại cửa hàng",searchPlaceholder:"Tìm kiếm thiết bị...",emptyText:"Không tìm thấy thiết bị nào.",disabled:n||h,className:"w-full"})}),e.jsx(d,{})]})})})]}),e.jsxs("div",{className:"grid grid-cols-12 items-start gap-4",children:[e.jsx("div",{className:"col-span-3 pt-2",children:e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Thiết bị đang hoạt động"})}),e.jsx("div",{className:"col-span-9",children:e.jsx(T,{type:"button",variant:"link",className:"h-auto p-0 text-blue-600 hover:text-blue-800",onClick:m,disabled:n,children:"Thêm thiết bị"})})]})]})]})}function tn({form:s,isLoading:n=!1,mode:t,storeId:a}){return e.jsx(ge,{...s,children:e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ss,{form:s,isLoading:n,mode:t})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Os,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Vs,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ps,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(As,{form:s,isLoading:n,storeUid:a})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ms,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Pe,{form:s,isLoading:n})}),e.jsx(sn,{form:s,isLoading:n}),t==="edit"&&e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(nn,{form:s,isLoading:n,storeUid:a})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Ts,{form:s,isLoading:n})}),e.jsx("div",{className:"rounded-lg border bg-white p-6",children:e.jsx(Is,{form:s,isLoading:n})})]})})}function Bn({storeId:s}){const{form:n,isRequiredFieldsValid:t,isEditMode:a,isLoadingStoreData:h}=ds({storeId:s}),{handleBack:o,handleSave:m,isCreating:x}=xs({formData:n.getValues(),storeId:s}),{data:l,isExpired:u,isActive:p}=de({storeId:s,enabled:a}),{handleToggleStatus:f,isUpdating:j}=_s(),N=a?M.PAGE_TITLE_EDIT:M.PAGE_TITLE_CREATE,I=a&&u,k=()=>{m()},v=()=>{l&&f(l)},b=()=>{n.handleSubmit(k)()};return e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsx(Ns,{isVisible:I}),e.jsx(ys,{title:N,onBack:o,children:e.jsx(fs,{isEditMode:a,isExpired:u,isActive:p,isUpdating:j,onToggleStatus:v,isRequiredFieldsValid:t,isCreating:x,onSave:b})}),e.jsx("div",{className:"mx-auto max-w-4xl",children:e.jsx(tn,{form:n,mode:a?"edit":"add",isLoading:h,storeId:s})})]})}export{Bn as S};
